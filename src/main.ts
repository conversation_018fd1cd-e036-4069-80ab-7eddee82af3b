import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import pinia from './store'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import { MCBaseStore } from '@simplex/simple-base';
import { statInit, sendWarning, errorToObject } from '@/common/utils';

let host = {
    swallow: 'https://swallow.kakamobi.cn/',
    login: 'https://auth.kakamobi.com/',
    tiku: 'https://jk-tiku.kakamobi.cn',
    parrot: 'https://parrot.kakamobi.cn'
};
let testHost = {
    swallow: 'https://swallow.ttt.mucang.cn/',
    login: 'https://auth.kakamobi.com/',
    tiku: 'https://jk-tiku.ttt.mucang.cn',
    parrot: 'https://parrot.ttt.mucang.cn'
};

MCBaseStore.setHosts(import.meta.env.MODE === "development" ? testHost : host);

statInit()

const app = createApp(App);
app.directive('scrollToView', (el, binding) => {
    if (binding.value) {
        const offsetTop = el.offsetTop;
        const offsetLeft = el.offsetLeft;
        const parent = el.parentNode
        parent.scroll({ behavior: 'smooth', left: offsetLeft, top: offsetTop });
    }
})
app.directive('wheelScrollX', {
    mounted(el) {
        el.addEventListener('wheel', function(event: WheelEvent) {
            event.preventDefault();
    
            el.scrollLeft += event.deltaY;
        });
    }
})
app.directive('onUserScroll', {
    mounted(el, binding) {
        let timer: ReturnType<typeof setTimeout>;
        ['wheel', 'touchmove', 'keydown'].forEach(eventType => {
            el.addEventListener(eventType, () => {
                clearTimeout(timer)
                binding.value(true);
                timer = setTimeout(() => {
                    binding.value(false);
                }, 200);
            });
        });
    }
})

if (!import.meta.env.DEV) {
    app.config.errorHandler = (err, _instance, info) => {
        sendWarning(1, {
            info,
            content: errorToObject(err as Error)
        })
    }
}

app.use(Antd)
app.use(pinia)
app.use(router)
app.mount('#app')
