<script setup lang="ts">
import { watch, onMounted, toRaw } from 'vue';
import { storeToRefs } from 'pinia';
import {assign} from 'lodash-es'
import TeachPlanC from './components/teachPlanC/index.vue';
import { trackEvent } from '@/common/utils';
import userPlanState from '@/store/modules/planState';

const planState = userPlanState()
const { teachPlanData } = storeToRefs(planState);

let index = 0;
let isExternalUpdate = false;

onMounted(() => {
    trackEvent({
        fragmentName1: '课件详情',
        actionType: '触发',
        actionName: '页面初始化',
        pageUrl: location.href,
    })
})

window.TIWH5WebCtrl.on(
    'TIW_H5WEB_DATA',
    async (data: any) => {
        console.log('接受：', data, index);
        index++;
        isExternalUpdate = true
        assign(teachPlanData.value, data.teachPlanData)
        setTimeout(() => { isExternalUpdate = false; }, 0);
    }
);

const sendTIWH5WebCtrlMessage = () => {
    const params: any = {
        teachPlanData: toRaw(teachPlanData.value),
    };

    console.log('send params:', +new Date(), params);
    window.TIWH5WebCtrl.syncData(params);

    trackEvent({
        fragmentName1: '课件详情',
        actionType: '触发',
        actionName: '信令同步',
        pageUrl: location.href,
        syncData: params,
    })
}

watch(teachPlanData, () => {
    if (!isExternalUpdate) {
        sendTIWH5WebCtrlMessage();
    }
}, {
    deep: true
});

</script>

<template>
    <div class="page-teach-plan">
        <div class="teach-box">
            <TeachPlanC widthMode="full" />
        </div>
    </div>
</template>

<style lang="less" scoped>
.page-teach-plan {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    font-weight: 700;
    .teach-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
    }
}
</style>
