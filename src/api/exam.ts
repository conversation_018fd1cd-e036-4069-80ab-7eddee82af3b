import { request } from "@/common/request";

export function getExamQidList(params: { contentId: string }) {

    return request({
        hostName: 'parrot',
        url: '/api/web/pts-lesson-examine/question-ids.htm',
        data: {
            examineEncodeId: params.contentId
        }
    }).then(data => {
        return data.value?.split(',') || []
    });
};


export function submitExamStore(params: { examineEncodeId: string, errorqIds: string, rightqIds: string, answers: string }) {

    return request({
        hostName: 'parrot',
        url: '/api/web/pts-lesson-examine/submit-answers.htm',
        method: "POST",
        data: params
    })
};