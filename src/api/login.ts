import { request } from "../common/request";
import { statInit } from "../common/utils";

/** 获取swallow远程配置,已购买页获取远程配置的kemu是写死的，所以需要传递参数kemu */
export function smsCheck(data: {
    _appName: string,
    _platform: string,
    _authVersion: number,
    phoneNumber: string,
    NECaptchaValidate: string
}) {
    return request({
        hostName: 'login',
        url: 'api/web/v3/login-sms/check.htm',
        method: 'POST',
        data: {
            ...data
        }
    });
}

export function codeLogin(data: {
    _appName: string,
    _platform: string,
    _authVersion: number,
    smsCode: string,
    smsId: string,
    phoneNumber: string
}) {
    return request({
        hostName: 'login',
        url: 'api/web/v3/login-sms/login.htm',
        method: 'POST',
        data: {
            ...data
        }
    }).then((data) => {
        statInit()
        return data
    });
}