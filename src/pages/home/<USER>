<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { WrongListType, CoursewareType } from '@/common/const';
import { onMounted, ref } from 'vue';
import { getListPlanContent, HomeItem, TeachType, HomeStatus } from '@/api/teach';
import { dateFormat } from '@/common/utils';

const router = useRouter();
const route = useRoute();

const contentList = ref<HomeItem[]>([]);
const pageStatus = ref<HomeStatus>(HomeStatus.NoContent);
const teachName = ref<string>('');
const readyTime = ref<string>('');
const subject = ref('');

onMounted(() => {
    getData();
});

const getData = () => {
    getListPlanContent({ courseId: +route.params.courseId! }).then((data) => {
        contentList.value = data.itemList;
        pageStatus.value = data.status;
        teachName.value = data.lecturerNickName;
        readyTime.value = dateFormat(data.readyTime, 'yyyy-MM-dd hh:mm');
        subject.value = data.planName;
    });
};

const onGoCategory = (id: number) => {
    router.push({
        name: 'category',
        query: {
            contentId: id,
        },
    });
};

const onGoTeachPlan = (type = CoursewareType.Special, id: number) => {
    router.push({
        name: 'teachPlan',
        query: {
            coursewareType: type,
            id,
        },
    });
};

const onGoWrongList = async (type = WrongListType.Knowledge, id: number, name?: string) => {
    router.push({
        name: 'wrongList',
        query: {
            wrongListType: type,
            contentId: id,
            name: name,
        },
    });
};

const onGoExam = (id: number) => {
    router.push({
        name: 'exam',
        query: {
            contentId: id,
        },
    });
};
</script>

<template>
    <div class="hpage">
        <div class="top">
            <div class="topl">
                <div class="d1"></div>
                <div class="d2">
                    <span>{{ subject }}</span>
                </div>
            </div>
            <!-- <div class="topr"></div> -->
        </div>
        <div class="content" v-if="pageStatus === HomeStatus.Success">
            <template v-for="item in contentList">
                <template v-if="item.type === TeachType.Wrong && item.displaySpecial">
                    <div class="section sec1" @click="onGoCategory(item.contentId)">
                        <span class="sp">讲错题</span>
                        <i class="action">去学习 ></i>
                        <label class="tag"></label>
                    </div>
                </template>
                <template v-else-if="item.type === TeachType.Wrong">
                    <div class="section sec1" @click="onGoWrongList(WrongListType.Knowledge, item.contentId, '所有错题')">
                        <span class="sp">所有错题<span v-if="item.scope" style="font-size: 14px;">(近{{item.scope}}次模考)</span></span>
                        <i class="action">去学习 ></i>
                        <label class="tag"></label>
                    </div>
                </template>
                <!-- <template v-if="item.type === TeachType.Exam">
                    <div class="section sec2" @click="onGoExam(item.contentId)">
                        <span class="sp">随堂测验</span>
                        <i class="action">去测验 ></i>
                        <label class="tag"></label>
                    </div>
                </template> -->
                <template v-if="item.type === TeachType.Knowledge">
                    <div class="section sec3" @click="onGoWrongList(WrongListType.Other, item.contentId)">
                        <span class="sp">知识点巩固</span>
                        <i class="action">去学习 ></i>
                        <label class="tag"></label>
                    </div>
                </template>
                <template v-if="item.type === TeachType.Special && item.contentList?.length">
                    <div class="section sec4">
                        <span class="sp">专项讲解</span>
                        <div v-for="ele in item.contentList" class="item-w" @click="onGoTeachPlan(CoursewareType.Special, ele.id)">
                            <div class="item">
                                <label class="lbg"></label>
                                <span class="txt">{{ ele.name }}</span>
                                <i class="rbg"></i>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-if="item.type === TeachType.Sprint">
                    <div class="section sec4">
                        <span class="sp">临考冲刺</span>
                        <div v-for="ele in item.contentList" class="item-w" @click="onGoTeachPlan(CoursewareType.Exam, ele.id)">
                            <div class="item">
                                <label class="lbg"></label>
                                <span class="txt">{{ ele.name }}</span>
                                <i class="rbg"></i>
                            </div>
                        </div>
                    </div>
                </template>
            </template>
        </div>
        <div class="none" v-if="pageStatus === HomeStatus.NoContent">
            <div class="div1"></div>
            <div class="div2">当前课程没有推荐的教学内容</div>
            <div class="div3">请按照学员学习情况组织本节课教学内容</div>
        </div>
        <div class="preparing" v-if="pageStatus === HomeStatus.Working">
            <div class="div1"></div>
            <div class="div2">内容正在准备中</div>
            <div class="div3">
                预计<i>{{ readyTime }}</i
                >准备完成
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.hpage {
    background: url(./images/8.png) no-repeat right top/624px 400px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 30px;
        height: 400px;
        .topl {
            display: flex;
            flex-direction: column;
            .d1 {
                width: 412px;
                height: 125px;
                background: url(./images/7.png) no-repeat;
                background-size: 100% 100%;
            }
            .d2 {
                display: flex;
                margin-top: 30px;
                span {
                    font-size: 19px;
                    padding: 15px 30px;
                    border: 1px solid rgba(0, 0, 0, 0.06);
                    border-radius: 29px;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 26px;
                }
            }
        }
    }

    .content {
        margin-top: auto;
        display: flex;
        overflow-x: auto;
        .section {
            cursor: pointer;
            flex-shrink: 0;
            margin-left: 20px;
            width: 285px;
            height: 400px;
            border: 1px solid #ffffff;
            border-radius: 24px;
            backdrop-filter: blur(5px);
            display: flex;
            flex-direction: column;
            align-items: center;
            .sp {
                font-size: 28px;
                font-weight: bold;
                text-align: center;
                color: rgba(0, 0, 0, 0.85);
                line-height: 40px;
                margin-top: 45px;
            }

            .action {
                font-size: 17px;
                text-align: center;
                color: rgba(0, 0, 0, 0.65);
                line-height: 24px;
                padding: 8px 30px;
                border: 1px solid rgba(0, 0, 0, 0.04);
                border-radius: 21px;
                margin-top: 20px;
            }
            .tag {
                width: 220px;
                height: 220px;
                margin-top: 50px;
            }
        }
        .sec1 {
            background: linear-gradient(180deg, #ffeaf0 0%, #ffffff 50%, #ffffff 100%);
            label {
                background: url(./images/1.png) no-repeat;
                background-size: 100% 100%;
            }
        }
        .sec2 {
            background: linear-gradient(180deg, #f0edff 0%, #ffffff 50%, #ffffff 100%);
            label {
                background: url(./images/2.png) no-repeat;
                background-size: 100% 100%;
            }
        }
        .sec3 {
            background: linear-gradient(180deg, #dcf8ff 0%, #f7fdff 50%, #ffffff 100%);
            label {
                background: url(./images/3.png) no-repeat;
                background-size: 100% 100%;
            }
        }
        .sec4 {
            background: linear-gradient(180deg, #dcf2ff 0%, #ffffff 50%, #ffffff 100%);
            .sp {
                padding-bottom: 20px;
            }
            .item-w {
                width: 100%;
                padding: 0 20px 15px 20px;
            }
            .item {
                padding: 15px 15px 15px 20px;
                display: flex;
                align-items: center;
                border: 1px solid rgba(0, 0, 0, 0.04);
                border-radius: 12px;
                &.active {
                    background: linear-gradient(90deg, #30adff, #80d2ff);
                    .lbg {
                        background: url(./images/5.png) no-repeat;
                        background-size: 100% 100%;
                    }
                    .rbg {
                        background: url(./images/9.png) no-repeat;
                        background-size: 100% 100%;
                    }
                }
                .lbg {
                    width: 25px;
                    height: 30px;
                    background: url(./images/4.png) no-repeat;
                    background-size: 100% 100%;
                }
                .txt {
                    font-size: 18px;
                    text-align: left;
                    color: #008aff;
                    line-height: 25px;
                    flex: 1;
                    padding-left: 15px;
                }
                .rbg {
                    width: 26px;
                    height: 26px;
                    background: url(./images/6.png) no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }
    .none {
        height: 480px;
        background: #ffffff;
        border: 1px solid #ffffff;
        border-radius: 24px;
        backdrop-filter: blur(5px);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .div1 {
            width: 144px;
            height: 144px;
            background: url(./images/11.png) no-repeat;
            background-size: 100% 100%;
        }
        .div2 {
            font-size: 22px;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.85);
            line-height: 30px;
            margin-top: 15px;
        }
        .div3 {
            font-size: 18px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 25px;
            margin-top: 10px;
        }
    }
    .preparing {
        height: 480px;
        background: #ffffff;
        border: 1px solid #ffffff;
        border-radius: 24px;
        backdrop-filter: blur(5px);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .div1 {
            width: 144px;
            height: 144px;
            background: url(./images/10.png) no-repeat;
            background-size: 100% 100%;
        }
        .div2 {
            font-size: 22px;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.85);
            line-height: 30px;
            margin-top: 15px;
        }
        .div3 {
            font-size: 18px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 25px;
            margin-top: 10px;
            i {
                color: #04a5ff;
            }
        }
    }
}
</style>
