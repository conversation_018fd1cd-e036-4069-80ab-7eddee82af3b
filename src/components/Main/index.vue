<script setup lang="ts">
// import { getListPlanContent } from '@/api/teach';
import Header from '@/components/header/index.vue';
import useTargetSize from '@/hooks/useTargetSize';
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const {scaleStyle} = useTargetSize()

// const teachName = ref<string>('');

onMounted(() => {
    // getData();
});

// const getData = () => {
//     getListPlanContent({ courseId: +route.params.courseId! }).then((data) => {
//        teachName.value = data.lecturerNickName
//     });
// };
</script>

<template>
    <a-watermark :font="{ color: 'rgba(51,51,51,0.08)' }" :content="'驾考宝典·' + (route.params.courseId || '')" class="water-mark" style="">
        <div class="main" :style="scaleStyle">
            <Header />
            <div class="content">
                <router-view></router-view>
            </div>
        </div>
    </a-watermark>
</template>

<style lang="less" scoped>
.water-mark {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.main {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    .content {
        flex: 1;
        padding: 0 30px 30px;
        box-sizing: border-box;
        overflow: auto;
    }
}
</style>
