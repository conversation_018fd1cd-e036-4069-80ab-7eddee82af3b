<template>
    <div class="course-plan-page">
        <div class="head">
            <div></div>
            <div>
                驾考私教授课系统
            </div>
            <a-dropdown :trigger="['click']">
                <a-avatar :src="avatarUrl" />
                <template #overlay>
                    <a-menu>
                        <a-menu-item @click="logout">
                            退出
                        </a-menu-item>
                    </a-menu>
                </template>
            </a-dropdown>
        </div>
        <div class="content-box">
            <div class="total-box">
                <div class="info-item">
                    课程总数： <text class="num">{{ totalData.totalCount }}</text>
                </div>
                <div class="info-item">
                    已完成课程数：<text class="num">{{ totalData.finishedCount }}</text>
                </div>
                <div class="info-item">
                    已结算课程数：<text class="num">{{ totalData.settleCount }}</text>
                </div>
                <div class="info-item">
                    待结算课程数：<text class="num">{{ totalData.waitSettleCount }}</text>
                </div>
            </div>
            <div class="table-box">
                <a-table :dataSource="dataSource" :columns="columns" :pagination="tablePagination" @change="handleTableChange" rowKey="courseId" size="small" bordered class="cp-table">
                    <template #bodyCell="{ column, record, text }">
                        <template v-if="column.key === 'homeworks'">
                            <template v-if="record.status !== StatusEnum.NEW">
                                <template v-if="text?.length">
                                    <div v-for="(item, index) in text">
                                        作业{{ index+1 }}:
                                        <template v-if="item.type === 3">错题扫清</template>
                                        <template v-else-if="item.type === 1">模拟考试{{ item.bizValue }}次</template>
                                        <template v-else-if="item.type === 4">{{ generateContent(item.bizValue) }}</template>
                                        <template v-else>专项练习:{{ item.bizName }}</template>
                                    </div>
                                </template>
                                <template v-else>
                                    <a-button type="link" @click="onPushHomeWork(record)">去布置</a-button>
                                </template>
                            </template>
                        </template>
                        <template v-if="column.key === 'operation'">
                            <template v-if="record.status === StatusEnum.NEW">
                                <a-button type="link" @click="onAcceptCourse(record)">接受</a-button>
                            </template>
                            <template v-else-if="record.status === StatusEnum.ACCEPTED">
                                <a-button type="link" @click="onGoLession(record)">去上课</a-button>
                                <a-button type="link" @click="onLookTeach(record)">查看教案</a-button>
                                <a-button type="link" @click="onCopyUrl(record)">复制上课链接</a-button>
                                <a-button type="link" @click="onCopyQrcode(record)">二维码</a-button>
                                <a-button type="link" @click="onGoReviewCourse(record)">发起上课验收</a-button>
                            </template>
                        </template>
                    </template>
                </a-table>
            </div>
            <a-modal v-model:open="showHomeWorkModel" :destroyOnClose="true" :maskClosable="false" :confirmLoading="loading" @ok="onSubmitHomeWork" :okButtonProps="{
                disabled: !(homeWorkFormData.checked || homeWorkFormData.examNum > 0 || homeWorkFormData.specialExercise.length > 0 || (homeWorkFormData.examScore?.scopeType && homeWorkFormData.examScore?.times > 0 && homeWorkFormData.examScore?.score > 0))
            }">
                <a-form ref="formRef" :model="homeWorkFormData" name="dynamic_rule" :label-col="{ style: { width: '128px' } }">
                    <a-form-item label="错题扫清1" name="checked">
                        <a-switch v-model:checked="homeWorkFormData.checked" />
                    </a-form-item>
                    <a-form-item label="模拟考试（次数）" name="examNum">
                        <a-input-number v-model:value="homeWorkFormData.examNum" min="0" />
                    </a-form-item>
                    <a-form-item label="专项练习" name="specialExercise">
                        <a-checkbox-group v-model:value="homeWorkFormData.specialExercise" name="specialExercise" style="display: flex;">
                            <a-row>
                                <a-col :span="8" style="padding: 5px 0" v-for="item in specialList">
                                    <a-checkbox :value="item.id">{{ item.name }}</a-checkbox>
                                </a-col>
                            </a-row>
                        </a-checkbox-group>
                    </a-form-item>
                    <a-form-item label="模拟考试（成绩）">
                        <a-form-item style="margin-bottom: 0;" class="mb10">
                            <a-select :options="scopeTypeOptions" v-model:value="homeWorkFormData.examScore.scopeType" :allowClear="true" placeholder="未开启" style="width: 120px;"></a-select>
                        </a-form-item>
                        <a-form-item-rest>
                            <a-form layout="inline" v-if="homeWorkFormData.examScore.scopeType">
                                <a-form-item label="次数" :required="!!homeWorkFormData.examScore.scopeType">
                                    <a-input-number v-model:value="homeWorkFormData.examScore.times" min="0" />
                                </a-form-item>
                                <a-form-item label="成绩" :required="!!homeWorkFormData.examScore.scopeType">
                                    <a-input-number v-model:value="homeWorkFormData.examScore.score" min="0" />
                                </a-form-item>
                            </a-form>
                        </a-form-item-rest>
                    </a-form-item>
                </a-form>
            </a-modal>
            
            <a-modal v-model:open="showReviewCourseModel" :destroyOnClose="true" :maskClosable="false" :confirmLoading="loading" @ok="onSubmitReviewCourse">
                <a-form ref="reviewCourseFormRef" :model="reviewCourseFormData" name="dynamic_rule">
                    <div style="font-size: 18px;">课后评语</div>
                    <div style="color: #666; padding-bottom: 16px;">请简单总结本节课内容和学员表现（学员本人不可见）</div>
                    <a-form-item label="课程主题" name="main" required>
                        <a-textarea v-model:value="reviewCourseFormData.main" placeholder="这节课讲解了什么内容？如: 扣分+罚款/错题扫清/专项题目讲解" />
                    </a-form-item>
                    <a-form-item label="学情分析" name="analyze" required>
                        <a-textarea v-model:value="reviewCourseFormData.analyze" placeholder="配合度高/低，群内及上课是否积极互动？作业练习是否配合完成？" />
                    </a-form-item>
                    <a-form-item label="问题反馈" name="feedback" required>
                        <a-textarea v-model:value="reviewCourseFormData.feedback" :rows="3" placeholder="突发情况或需要向教务、主管老师发言的问题。如：学员课上做其他事情、厌学情绪、退费情绪、上课环境嘈杂、部分字不认识、约考时间信息错误、学员习惯性记答案等" />
                    </a-form-item>
                </a-form>
            </a-modal>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getLecturerCourseListStore, getSpecialListStore, getCourseStatisticStore, setHomeWork, acceptCourse, submitReviewCourse, getRoomInfo } from '@/api/teach';
import { copyTextToClipboard, copyURLToQRCodeAndClipboard, dateFormat, makeToast } from '@/common/utils';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

enum StatusEnum {
    DRAFT = 1,
    NEW = 2,
    ACCEPTED = 3,
    WAIT_AUDIT = 6,
    REJECT = 7,
    PASS = 8,
    ABANDON = 9,
    ABEND = 10,
}
const StatusTextMap = {
    [StatusEnum.DRAFT]: '草稿',
    [StatusEnum.NEW]: '待接受',
    [StatusEnum.ACCEPTED]: '已接受',
    [StatusEnum.WAIT_AUDIT]: '待验收',
    [StatusEnum.REJECT]: '验收驳回',
    [StatusEnum.PASS]: '验收通过',
    [StatusEnum.ABANDON]: '已废弃',
    [StatusEnum.ABEND]: '异常终止',
};
const router = useRouter();

const totalData = ref({
    totalCount: 0,
    finishedCount: 0,
    settleCount: 0,
    waitSettleCount: 0,
});

const scopeTypeOptions = ref([
    {
		label: '入学后',
		value: 1
	},
	{
		label: '本节课后',
		value: 2
	}
])
const scopeMap: { [key: number]: string } = scopeTypeOptions.value.reduce((acc, value) => {
    acc[value.value] = value.label;
    return acc;
}, {} as { [key: number]: string });


// 表格相关数据 start
const columns = [
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        customRender: ({ text }: { text: StatusEnum }) => {
            return StatusTextMap[text];
        },
    },
    {
        title: '课程主题',
        dataIndex: 'subject',
        width: 150,
    },
    {
        title: '学员姓名',
        dataIndex: 'studentName',
    },
    {
        title: '上课开始时间',
        dataIndex: 'beginTime',
        customRender: ({ text }: { text: Date }) => {
            return dateFormat(text, 'yyyy-MM-dd hh:mm:ss');
        },
    },
    {
        title: '上课结束时间',
        dataIndex: 'endTime',
        customRender: ({ text }: { text: Date }) => {
            return dateFormat(text, 'yyyy-MM-dd hh:mm:ss');
        },
    },
    {
        title: '督导老师',
        dataIndex: 'supervisorName',
        width: 80,
    },
    {
        title: '课后作业',
        dataIndex: 'homeworks',
        key: 'homeworks',
    },
    {
        title: '操作',
        key: 'operation',
        width: 170,
    },
];
const dataSource = ref<
    {
        status: StatusEnum;
        [x: string]: any;
    }[]
>([]);
const tablePagination = ref<any>({
    current: 1,
    total: 0,
    pageSize: 25,
});

// 表格相关数据 end

const operationLine = ref({});
const avatarUrl = ref(JSON.parse(localStorage.getItem('mucang_userInfo') || '{}')?.avatar || '');
// 课后作业start
const specialList = ref<any[]>([]);
const showHomeWorkModel = ref<boolean>(false);
const showReviewCourseModel = ref<boolean>(false);
const loading = ref<boolean>(false);
const initHomeWorkFormData = {
    checked: false,
    examNum: 0,
    specialExercise: [],
    examScore: {
        scopeType: null,
        times: 0,
        score: 0,
    },
	specialSource: 1,
}
const homeWorkFormData = ref<{
    checked: boolean;
    examNum: number;
    specialExercise: number[];
    examScore: {
        scopeType: number | null,
        times: number,
        score: number,
    };
	specialSource: number,
}>(initHomeWorkFormData);

const initReviewCourseFormData = {
    main: '',
    analyze: '',
    feedback: '',
}
const reviewCourseFormData = ref<{
    main: string,
    analyze: string,
    feedback: string,
}>(initReviewCourseFormData)

// 课后作业end
const formRef = ref();
const reviewCourseFormRef = ref()

function generateContent(data: string) {
    const content = JSON.parse(data)
    return `${scopeMap[content.scopeType]}有${content.times}次模考成绩≥${content.score}`;
}
const logout = () => {
    localStorage.removeItem('mucang_userInfo');
    setTimeout(() => {
        location.replace(`${location.origin}${location.pathname}?${location.hash.replace(/(\#(\/\d+)?\/).+/, '$1login')}`);
    }, 1000)
}
const getTableSource = () => {
    const params = {
        page: tablePagination.value.current,
        limit: tablePagination.value.pageSize,
    };

    getLecturerCourseListStore(params).then((data) => {
        tablePagination.value.total = data.paging.total;

        dataSource.value = data.data.itemList;
    });
};

const getSpecialExerciseList = () => {
    getSpecialListStore({
        courseId: operationLine.value.courseId!
    }).then(list => {
        specialList.value = list
        homeWorkFormData.value.specialSource = list[0].specialSource
    })
};
const getCourseStatistic = () => {
    getCourseStatisticStore().then((data) => {
        totalData.value = data;
    });
};

onMounted(() => {
    getCourseStatistic();
    getTableSource();
});

const onSubmitHomeWork = async () => {
    await formRef.value.validateFields();
    const formData = homeWorkFormData.value;
    const homeworkList = [];

    if (formData.checked) {
        homeworkList.push({
            type: 3,
            bizValue: true,
        });
    }

    if (formData.examNum) {
        homeworkList.push({
            type: 1,
            bizValue: formData.examNum,
        });
    }

    if (formData.examScore?.scopeType) {
        if (!(formData.examScore.times && formData.examScore.score)) {
            makeToast('请填写模考次数&成绩');
            return
        }
        homeworkList.push({
            type: 4,
            bizValue: JSON.stringify(formData.examScore),
        });
    }
    formData.specialExercise.forEach((item) => {
        homeworkList.push({
            type: 2,
            bizValue: item,
        });
    });

    loading.value = true
    setHomeWork({
        courseScheduleId: operationLine.value.courseId!,
        homeworkList: JSON.stringify(homeworkList),
        specialSource: formData.specialSource
    }).then((data) => {
        loading.value = false
        if (data.value) {
            makeToast('作业布置成功');
            showHomeWorkModel.value = false;
            getTableSource();
        } else {
            makeToast('作业布置失败，请稍后重试');
        }
    }).catch(() => {
        loading.value = false
    });
};
const onSubmitReviewCourse = async () => {
    await reviewCourseFormRef.value.validateFields();
    const formData = reviewCourseFormData.value;
    
    loading.value = true
    submitReviewCourse({
        courseId: operationLine.value.courseId!,
        comment: `课程主题:${formData.main}；学情分析:${formData.analyze}；问题反馈:${formData.feedback}`
    }).then((data) => {
        loading.value = false
        if (data.value) {
            makeToast('上课验收提交成功');
            showReviewCourseModel.value = false;
            getTableSource();
        } else {
            makeToast('上课验收提交，请稍后重试');
        }
    }).catch(() => {
        loading.value = false
    });
}
const handleTableChange = (pagination: any) => {
    tablePagination.value = pagination;
    getTableSource();
};

const onPushHomeWork = (record: any) => {
    specialList.value = []
    operationLine.value = record;
    homeWorkFormData.value = JSON.parse(JSON.stringify(initHomeWorkFormData));
    getSpecialExerciseList();
    showHomeWorkModel.value = true;
};

const onGoLession = (record: any) => {
    const {href} = router.resolve({
        name: 'teachSetout',
        params: {
            courseId: record.courseId,
        },
    });
    // const fullUrl = location.origin + location.pathname + location.search + href
    window.open(href, '_blank')
};
const onLookTeach = (record: any) => {
    const {href} = router.resolve({
        name: 'home',
        params: {
            courseId: record.courseId,
        },
    });
    // const fullUrl = location.origin + location.pathname + location.search + href
    window.open(href, '_blank')
};
const onCopyUrl = (record: any) => {
    const host = 'https://laofuzi.kakamobi.com/'
    const url = `${host}personal-training-live/?roomNo=${record.courseNo}&courseId=${record.courseId}`
    copyTextToClipboard(url);
};
const onCopyQrcode = async (record: any) => {
    // const isProd = import.meta.env.MODE === 'production'
    // const host = isProd ? 'https://laofuzi.kakamobi.com/' : 'https://laofuzi.ttt.kakamobi.com/'
    const host = 'https://laofuzi.kakamobi.com/'
    const url = `${host}personal-training-live/?roomNo=${record.courseNo}&courseId=${record.courseId}`
    await copyURLToQRCodeAndClipboard(url);
    makeToast('复制二维码成功！');
};

const onAcceptCourse = (record: any) => {
    acceptCourse({courseId: record.courseId}).then(data => {
      if (data.value) {
        makeToast('接受成功！');
        getTableSource();
      } else {
        makeToast('接受失败，请稍后重试');
      }
    })
}

const onGoReviewCourse = async (record: any) => {
    const {status} = await getRoomInfo({roomNo: record.courseNo})
    if (status !== 3) {
        makeToast('您未使用授课工具进行上课，请使用小程序上传授课视频并发起验收。');
        return
    }
    operationLine.value = record;
    reviewCourseFormData.value = JSON.parse(JSON.stringify(initReviewCourseFormData));
    showReviewCourseModel.value = true;
}
</script>
<style lang="less" scoped>
.course-plan-page {
    height: 100%;
    padding-bottom: 30px;
    background: linear-gradient(180deg, #0e0c17, #12132e);
    display: flex;
    flex-direction: column;
    .head {
        height: 72px;
        background-color: #1d224c;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
    }
    .content-box {
        width: 1200px;
        flex: 1;
        padding: 30px;
        box-sizing: border-box;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 8px;
        margin-top: 30px;
        overflow: auto;
        .total-box {
            display: flex;
            .info-item {
                margin-right: 30px;
                font-size: 16px;
                .num {
                    color: #008aff;
                }
            }
        }
        .table-box {
            margin-top: 30px;
        }
    }
}
</style>
