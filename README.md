## 项目背景
驾考宝典私教业务在实际开展过程中，因为授课的讲师是第三方兼职，并且人数众多，因此我们需要一个可控的教学工具来帮助讲师完成教学任务。而电子教案作为授课工具的子项目，能够很好的完成该任务。

## 项目概述
驾考宝典私教电子教案是授课工具的一个子项目，包括错题讲解、知识点课件、学员模考详情等，帮助学员高效备考并提升考试成绩。

## 项目的术语定义
状态同步  
讲师在电子教案中进行的操作，通过IM信令同步，同步到另一个白板上，学员可以实时看到。  
知识点课件  
知识点课件是指在电子教案中，按知识点将图片上传到后台，方便错题讲解时讲解管理的知识点  

## 项目的部署
授课工具：https://laofuzi.kakamobi.com/personal-training-live/  
电子教案：https://laofuzi.kakamobi.com/personal-training-management/  
git：  
https://git.mucang.cn/jiakaobaodian-webfront/personal-training-management

## 项目的运行环境
运行在PC端，内嵌到腾讯白板中

## 项目的技术选型
Vue3+antd-vue+Vite

## 第三方SDK
h5webctrl  
腾讯云互动白板的信令同步插件  

## 项目功能
1. 错题列表  
支持多种错题数据来源类型，如“知识点错题”、“专项练习错题”、“模拟考试错题”等。  
支持查看错题统计信息，如答错次数、正确率等。  
支持随堂测验学员做题的数据，展示错题题目、选项、正确答案、解析等信息。  
支持查看相关知识点和切换其他课件进行学习。

2. 知识点课件  
包含知识点课件，和考前冲刺课件2种类型  
可以展示当前课件所有知识点列表、展示备注信息，并且支持跳转到对应知识点图片，以及根据当前错题情况知识点进行快速跳转

3. 模拟考试  
考试列表：  
展示学员的所有模拟考试记录，包括考试时间、分数、考试时长等信息。  
支持按有效成绩筛选考试记录。  
考试详情：  
展示考试题目列表，支持在线作答。  
提供倒计时功能，模拟真实考试环境。  
考试结束后展示成绩分析，包括答对题数、答错题数、得分等。  
支持查看错题和解析。

4. 数据可视化  
学习进度：  
使用 ECharts 绘制学习进度图表，展示学员的学习情况。  
图表类型包括折线图、散点图等，直观展示学员的成绩变化趋势。  
考试分析：  
分析学员的考试数据，生成考试报告。  
提供数据统计和图表展示，帮助学员了解自身学习状况。  

5. 课程管理  
课程列表：  
展示学员的课程安排，包括课程主题、上课时间、状态等信息。  
课程详情：  
展示课程详细信息，包括课程主题、上课时间、督导老师等。  
教案查看：  
展示教案详情，实现在线互动教学。