import { computed, ref } from "vue";
import { defineStore } from "pinia";

const userInfo = defineStore("mucang_userInfo", () => {
  const userInfo = ref();

  const getUserInfo = computed(() => userInfo.value)

  function setUserInfo(data: object) {
    userInfo.value = data;
  }

  return { userInfo, getUserInfo, setUserInfo };
}, {
  persist: {
    serializer: {
      serialize: (state) => JSON.stringify(state.userInfo),
      deserialize: (data) => {
        return {userInfo: JSON.parse(data)}
      },
    },
   }
});

export default userInfo;