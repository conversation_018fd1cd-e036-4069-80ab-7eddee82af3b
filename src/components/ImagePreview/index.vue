<script setup lang="ts">
import useTargetSize from '@/hooks/useTargetSize';

const {scaleStyle} = useTargetSize()

const showModel = defineModel('open', {
    type: Boolean,
    default: false
})

</script>

<template>
    <a-modal v-model:open="showModel" width="100%" wrap-class-name="full-modal" :footer="false" :closable="false">
        <div class="wrap" @click="showModel = false">
            <div class="image-preview" :style="scaleStyle">
                <div class="inner">
                    <div class="limit" @click.stop>
                        <slot></slot>
                        <div class="close" @click="showModel = false"></div>
                    </div>
                </div>
            </div>
        </div>
    </a-modal>
</template>

<style lang="less">
.full-modal {
    .ant-modal {
        max-width: 100%;
        top: 0;
        padding-bottom: 0;
        margin: 0;
        .ant-modal-content {
            padding: 0;
            display: flex;
            flex-direction: column;
            height: calc(100vh);
            background-color: transparent;
            box-shadow: none;
        }
        .ant-modal-body {
            height: 100%;
        }
    }
}
</style>
<style lang="less" scoped>
.wrap {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.image-preview {
    :deep(img) {
        max-width: calc(1280px - 64px);
        max-height: calc(720px - 36px);
    }
}
.inner {
    width: calc(100% - 64px);
    height: calc(100% - 36px);
    margin: 18px 0 0 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}
.limit {
    width: auto;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
}
.close {
    position: absolute;
    z-index: 1;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: url(../../assets/<EMAIL>) no-repeat center center/cover;
}
</style>
