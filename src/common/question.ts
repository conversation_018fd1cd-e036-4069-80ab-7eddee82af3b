import { Question } from "@/api/question"

enum Option {
    A = 'A',
    B = 'B',
    C = 'C',
    D = 'D',
    E = 'E',
    F = 'F',
    G = 'G',
    H = 'H'
}

enum OptionTypes {
    pd = '判断题',
    ax = '单选题',
    dx = '多选题'
}

interface optionItem {
    showKey: string,
    value: string,
    correct: boolean
}

export interface QuestionMap {
    selectList: Option[],
    optionTypes: OptionTypes[],
    question: Question,
    qId: number
    title: string
    mediaType: 0 | 1 | 2
    optionType: 0 | 1 | 2
    showOptionType: OptionTypes
    options: optionItem[]
    mediaContent: string
    correctOptions: any
    StrCorrect: string
    isCorrent: 1 | 2 | 3
    userSelect: any
    showExplain: string
    knowledgeIds: string
    knowledgeList?: {
        id: number,
        name: string
    }[]
    initQuestion(): void,
    getCorrectOptions(): optionItem[],
    getStrCorrect(): string,
    getUserAnser(): number
}

export const questionFn = function (question: Question, isCreate?: boolean) {

    const questionMap: QuestionMap = {
        selectList: [Option.A, Option.B, Option.C, Option.D, Option.E, Option.F, Option.G, Option.H],
        optionTypes: [OptionTypes.pd, OptionTypes.ax, OptionTypes.dx],
        question: question,
        qId: 0,
        title: '',
        mediaType: 0,
        showOptionType: OptionTypes.pd,
        options: [],
        mediaContent: '',
        correctOptions: undefined,
        StrCorrect: '',
        isCorrent: 1,
        userSelect: undefined,
        showExplain: '',
        knowledgeIds: '',
        initQuestion: function () {
            const options = this.selectList.map((item, index) => {
                let option = {};
                if (question[`option${item}`]) {
                    option = {
                        showKey: item,
                        value: question[`option${item}`],
                        // eslint-disable-next-line no-bitwise
                        correct: !!((16 << index) & question.answer)
                    };
                }
                return option;
            });
            // 当前题目的Id
            this.qId = question.questionId;

            // 展示题目标题
            this.title = question.question;
            // 展示媒体类型
            this.mediaType = question.mediaType;
            // 展示题目类型
            this.showOptionType = this.optionTypes[question.style];
            // 题目类型
            this.optionType = question.style;
            // 选项列表
            this.options = options as optionItem[];
            // 媒体数据
            this.mediaContent = question.mediaUrl;

            this.knowledgeIds = question.knowledgeIds;

            // 正确选项列表
            this.correctOptions = this.getCorrectOptions();
            // 正确选项字符串
            this.StrCorrect = this.getStrCorrect();
            // 题目的状态[1,2,3] 1表示未做 2表示做对了 3表示做错了
            this.isCorrent = 1;
            // 用户选择答案
            this.userSelect = {};

            this.showExplain = question.explain || '暂无';

        },
        getCorrectOptions() {
            return this.options.filter((item) => {
                return item.correct;
            });
        },
        getStrCorrect() {
            return this.getCorrectOptions().map(function (item) {
                return item.showKey;
            }).join(',');
        },
        getUserAnser() {
            let userAnswer = 0;

            this.selectList.forEach((item, index) => {
                if (this.userSelect[item]) {
                    userAnswer += 16 << index
                }
            })
            return userAnswer
        },
        optionType: 0
    };

    if (isCreate){
        let q = Object.create(questionMap)
        return Object.assign(q, question)
    }

    questionMap.initQuestion();

    return questionMap;
};


