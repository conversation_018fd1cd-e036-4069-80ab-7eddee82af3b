<script setup lang="ts">
import { watch, ref } from 'vue';
import { CoursewareType } from '@/common/const';
import { getCoursewareList } from '@/api/teach';

const showModel = defineModel('open', {
    type: Boolean,
    default: false
})
const props = defineProps({
    courseId: {
        type: String,
    },
});
const emit = defineEmits<{
    (e: 'onSelect', value: {type: CoursewareType, id: string}): void;
}>();

interface coursewareItem {
    bizId: string,
    name: string,
    type: CoursewareType,
    id: number,
}
let coursewareList = ref<coursewareItem[]>([])

async function getData() {
    const res = await getCoursewareList({
        courseId: props.courseId!
    })
    coursewareList.value = res.itemList
}
function onSelect(item: coursewareItem) {
    let {id} = item

    emit('onSelect', {type: 2, id: String(id)});
    showModel.value = false
}

watch(showModel, (val) => {
    if (val) {
        getData()
    }
})

</script>

<template>
    <a-modal v-model:open="showModel" title="全部专项" width="960px" centered>
        <div class="wrap">
            <div class="item" v-for="item in coursewareList" :key="item.id" @click="onSelect(item)">
                <label class="lbg"></label>
                <span class="txt">{{ item.name }}</span>
                <i class="rbg"></i>
            </div>
        </div>

        <template #footer></template>
    </a-modal>
</template>

<style lang="less" scoped>
.wrap {
    display: flex;
    flex-wrap: wrap;
}
.item {
    width: 32%;
    padding: 15px 15px 15px 20px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    margin: 6px;
    cursor: pointer;
    &.active {
        background: linear-gradient(90deg, #30adff, #80d2ff);
        .lbg {
            background: url(../../assets/5.png) no-repeat;
            background-size: 100% 100%;
        }
        .rbg {
            background: url(../../assets/9.png) no-repeat;
            background-size: 100% 100%;
        }
    }
    .lbg {
        width: 25px;
        height: 30px;
        background: url(../../assets/4.png) no-repeat;
        background-size: 100% 100%;
    }
    .txt {
        font-size: 18px;
        text-align: left;
        color: #008aff;
        line-height: 25px;
        flex: 1;
        padding-left: 15px;
    }
    .rbg {
        width: 26px;
        height: 26px;
        background: url(../../assets/6.png) no-repeat;
        background-size: 100% 100%;
    }
}
</style>
