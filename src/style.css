:root {
    font-family: '微软雅黑', Inter,system-ui,Avenir,Helvetica,Arial,sans-serif;
    line-height: 1.5;
    font-weight: 400;

    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);
    background-color: #242424;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}
a:hover {
    color: #535bf2;
}

body {
    margin: 0;
    display: flex;
    place-items: center;
    font-family: '微软雅黑', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

button {
    border-radius: 8px;
    /* border: 1px solid transparent; */
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    background-color: #1a1a1a;
    cursor: pointer;
    /* transition: border-color 0.25s; */
}
button:hover {
    /* border-color: #646cff; */
}
button:focus,
button:focus-visible {
    /* outline: 4px auto -webkit-focus-ring-color; */
}

.card {
    padding: 2em;
}

#app {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    text-align: center;
    background-color: #f6f9fe;
    background: linear-gradient(138deg,#e2f3ff 17%, #e1f2ff 81%, rgba(215,237,255,0.00) 98%);
    background-image: url(/src/assets/bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    top: 0;
}

@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }
    a:hover {
        color: #747bff;
    }
    button {
        background-color: #f9f9f9;
    }
}

::-webkit-scrollbar {
    width: 4px; /*  设置纵轴（y轴）轴滚动条 */
    height: 4px; /*  设置横轴（x轴）轴滚动条 */
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #e0f1ff;
}

::-webkit-scrollbar-track {
    border-radius: 0;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
    background: rgba(0, 0, 0, 0);
}
