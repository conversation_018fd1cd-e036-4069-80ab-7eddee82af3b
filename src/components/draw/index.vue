<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';


const props = defineProps({
    bgc: {
        type: String,
        default: 'rgba(0, 0, 0, 0.1)'
    }
})
const pageCanvasSize = reactive({ width: 0, height: 0 });
const $pageCanvas = ref();
let pageCtx: CanvasRenderingContext2D;

onMounted(() => {
    pageCanvasSize.width = $pageCanvas.value.clientWidth;
    pageCanvasSize.height = $pageCanvas.value.clientHeight;

    pageCtx = $pageCanvas.value.getContext('2d');
});

let allowWriting = false;

const pageDown = (e: MouseEvent) => {
    const { offsetX, offsetY } = e;

    pageCtx.strokeStyle = 'red';
    pageCtx.lineWidth = 3;
    pageCtx.beginPath();
    pageCtx.moveTo(offsetX, offsetY);
    allowWriting = true;
};
const pageMove = (e: MouseEvent) => {
    if (!allowWriting) {
        return;
    }
    const { offsetX, offsetY } = e;

    pageCtx.lineTo(offsetX, offsetY);

    pageCtx.stroke();
    pageCtx.save();
    pageCtx.beginPath();
    pageCtx.moveTo(offsetX, offsetY);
};
const pageUp = () => {
    if (!allowWriting) {
        return;
    }

    allowWriting = false;
};

const pageLeave = () => {
    if (!allowWriting) {
        return;
    }
    pageUp();
};
</script>

<template>
    <canvas ref="$pageCanvas" id="pageCanvas" @mousedown="pageDown" @mousemove="pageMove" @mouseup="pageUp" @mouseleave="pageLeave" :width="pageCanvasSize.width" :height="pageCanvasSize.height" :style="{
        backgroundColor: props.bgc
    }"></canvas>
</template>

<style scoped>
canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
</style>
