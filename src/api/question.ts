import { questionFn, QuestionMap } from "@/common/question"
import { request } from "@/common/request"

export interface Question {
    // 16进制答案
    answer: number
    // 关键字
    assuredKeywords: string
    // 章节id
    chapterId: number
    /**
     * 解析
    */
    explain: string
    // 当前题目的Id
    questionId: number
    // 题目
    question: string
    // 知识点
    knowledgeIds: string
    /**
     * 选项列表
    */
    optionA: string
    optionB: string
    optionC: string
    optionD: string
    optionE: string
    optionF: string
    optionG: string
    optionH: string
    /**
     * 媒体类型
    */
    mediaType: 0 | 1 | 2
    /**
    * 媒体数据
   */
    mediaUrl: string
    /**
     * 题目类型
    */
    style: 0 | 1 | 2
}


export function getTopicDetail(params: { qIdList: number[] }): Promise<QuestionMap[]> {
    return request({
        hostName: 'tiku',
        url: '/api/web/feedback/banner.htm',
        method: 'POST',
        data: {
            ids: params.qIdList.join(',')
        },
        serialize: data => {
            let questionList: QuestionMap[] = [];
    
            params.qIdList.forEach((id: number) => {
                let itemData: Question = { questionId: id, question: '题库精简升级，该试题已移除，无需继续练习。请练习其它试题，祝顺利拿本!' } as Question;
                data.itemList.forEach((item: any) => {
                    if (+id === item.questionId) {
                        itemData = item;
                    }
                });
    
                questionList.push(questionFn(itemData));
            });
            return questionList
        },
        others: {
            ajaxOptions: {
                processData: false,
                contentType: false
            }
        }
    })
};


export const getKnowledgeMap = (params: { questionList: QuestionMap[] }) => {
    const knowledgeSourceIdList: number[] = [];

    params.questionList.forEach(item => {
        item.knowledgeIds.split(',').forEach(ele => {
            if (knowledgeSourceIdList.indexOf(+ele) === -1) {
                knowledgeSourceIdList.push(+ele)
            }
        });
    })

    return request({
        hostName: 'tiku',
        url: '/api/web/knowledge/knowledge-detail-list.htm',
        data: {
            knowledgeSourceIdList: knowledgeSourceIdList.join(',')
        },
        serialize: data => {
            let knowledgeMap: {
                [x: number]: {
                    id: number,
                    name: string
                }
            } = {};
            data.itemList.forEach((item: any) => {
                knowledgeMap[item.sourceId] = {
                    id: item.sourceId,
                    name: item.name
                }
            });
    
            params.questionList.forEach(item => {
                let knowledgeList:any = [];
                item.knowledgeIds.split(',').forEach(ele => {
                    if (knowledgeMap[+ele]){
                        knowledgeList.push(knowledgeMap[+ele])
                    }
                });
    
                item.knowledgeList = knowledgeList;
            })
    
            return params.questionList;
        }
    })
}
