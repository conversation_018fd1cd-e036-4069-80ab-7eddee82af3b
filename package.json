{"name": "vite-project", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:test": "vue-tsc -b && npx vite build --mode development --sourcemap inline", "preview": "vite preview"}, "dependencies": {"@jiakaobaodian/jiakaobaodian-utils": "^1.1.8", "@ant-design/icons-vue": "^7.0.1", "@simplex/simple-base": "6.4.9", "@simplex/simple-base-sso": "3.0.2", "@simplex/simple-core": "4.0.22", "@simplex/simple-mcprotocol": "3.6.1", "@simplex/simple-mock": "1.0.1", "@simplex/simple-oort": "4.3.9", "ant-design-vue": "^4.2.3", "echarts": "^5.5.1", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qrcode": "^1.5.4", "swiper": "^11.1.8", "vue": "^3.4.29", "vue-router": "^4.4.0"}, "devDependencies": {"less": "^4.2.0", "less-loader": "^12.2.0", "@vitejs/plugin-vue": "^5.0.5", "rollup": "4.21.3", "typescript": "^5.2.2", "vite": "5.3.1", "vue-tsc": "2.0.21"}}