<script setup lang="ts">
import WrongList from './components/wrong-list/index.vue';
import Question from './components/question/index.vue';
import TeachPlanC from '@/pages/teachPlan/components/teachPlanC/index.vue';
import { ref, onMounted, nextTick, watch, toRaw } from 'vue';
import { storeToRefs } from 'pinia';
import {assign} from 'lodash-es'
import { WrongListType, CoursewareType } from '@/common/const';
import { trackEvent } from '@/common/utils';
import { useRoute } from 'vue-router';
import { getWrongList, getStudentSummaryWrongList, getMockExamWrongList, getCourseExamWrongList, kemuParamsMap, getSpecialQuestionList } from '@/api/teach';
import userPlanState from '@/store/modules/planState';

const route = useRoute();
const planState = userPlanState()
const { questionData, teachPlanData } = storeToRefs(planState);
let { contentId, id, wrongListType, specialId, uniqueId, categoryId, examineEncodeId, sno, tutorKemu, carType, specialSource } = route.query;
carType = carType || 'car'

const questionList = ref<
    {
        questionId: number;
        title: string;
        wrongCount: number;
    }[]
>([]);
let title = ref('');
const answers = ref<number[]>([]);
const TeachPlanCRef = ref();
const QuestionRef = ref();
let index = 0;
let isExternalUpdate = false;

window.TIWH5WebCtrl.on(
    'TIW_H5WEB_DATA',
    async (data: any) => {
        console.log('接受：', data, index);
        index++;
        isExternalUpdate = true
        assign(questionData.value, data.questionData)
        assign(teachPlanData.value, data.teachPlanData)
        setTimeout(() => { isExternalUpdate = false; }, 0);
    }
);

const sendTIWH5WebCtrlMessage = () => {
    const params: any = {
        questionData: toRaw(questionData.value),
        teachPlanData: toRaw(teachPlanData.value),
    };

    console.log('send params:', +new Date(), params);
    window.TIWH5WebCtrl.syncData(params);

    trackEvent({
        fragmentName1: '试题列表',
        actionType: '触发',
        actionName: '信令同步',
        pageUrl: location.href,
        syncData: params,
    })
}
watch([questionData, teachPlanData], () => {
    if (!isExternalUpdate) {
        sendTIWH5WebCtrlMessage();
    }
}, {
    deep: true
});

onMounted(() => {
    if (+wrongListType! === WrongListType.WrongQuestions) {
        getQuestionListForWrong('wrongQuestions')
        title.value = '错题列表'
    } else if (+wrongListType! === WrongListType.LastExamWrongQuestions) {
        getQuestionListForWrong('lastExamWrongQuestions')
        title.value = '错题列表'
    } else if (+wrongListType! === WrongListType.Exam) {
        getQuestionListForExam()
        title.value = '错题列表'
    } else if (+wrongListType! === WrongListType.SpecialPractice) {
        getQuestionListForSpecial()
        title.value = '专项练习'
    } else if (+wrongListType! === WrongListType.CourseExamResult) {
        getQuestionListForCourseExam()
        title.value = '随堂测验结果'
    } else {
        getQuestionList()
        title.value = '错题列表'
    }

    trackEvent({
        fragmentName1: '试题列表',
        actionType: '触发',
        actionName: '页面初始化',
        pageUrl: location.href,
    })
});

const getQuestionList = async () => {
    getWrongList({
        contentId: contentId as string,
        specialId: specialId as string,
        knowledgeId: id ? +id : null,
        needAllQuestions: +wrongListType! === WrongListType.AllQuestion,
    }).then((data) => {
        questionList.value = data.itemList;
    });
};

const getQuestionListForWrong = async (filed: string) => {
    getStudentSummaryWrongList({
        sno: sno as string,
        ...kemuParamsMap[tutorKemu],
        carType: carType,
        categoryId: +categoryId!,
        filed
    }).then((data) => {
        questionList.value = data.itemList;
    });
};

const getQuestionListForExam = async () => {
    getMockExamWrongList({
        sno: sno as string,
        ...kemuParamsMap[tutorKemu],
        carType: carType,
        uniqueId: uniqueId as string,
    }).then((data) => {
        questionList.value = data.itemList;
    });
};

const getQuestionListForCourseExam = async () => {
    getCourseExamWrongList({
        examineEncodeId: examineEncodeId as string,
    }).then((data) => {
        questionList.value = data.itemList;
        answers.value = data.answers
    });
};

const getQuestionListForSpecial = async () => {
    getSpecialQuestionList({
        specialId: specialId as string,
        courseId: +route.params.courseId,
        specialSource: specialSource as string,
    }).then((data) => {
        questionList.value = data.itemList;
    });
};

const onCloseTeach = () => {
    questionData.value.isOpenTeach = false;
};

const onGoKnowledge = async (knowledge: number | number[]) => {
    questionData.value.isOpenTeach = true;

    await nextTick();

    TeachPlanCRef.value && TeachPlanCRef.value.onChangeKnowledge(knowledge);
};

const onSelectCourseware = async (params: {type: CoursewareType, id: string}) => {
    questionData.value.isOpenTeach = true;

    await nextTick();

    TeachPlanCRef.value && TeachPlanCRef.value.onSelectCourseware(params);
};
</script>

<template>
    <div class="page-wrong-list">
        <div class="wrong-list-box" :class="{ wide: !questionData.isOpenTeach }">
            <WrongList :title="title" :questionList="questionList" />
        </div>
        <div class="question-teach-box">
            <div class="question-box">
                <Question ref="QuestionRef" :questionList="questionList" :answers="answers" @onGoKnowledge="onGoKnowledge" @onSelectCourseware="onSelectCourseware" />
            </div>
            <div class="teach-box" v-if="questionData.isOpenTeach">
                <div class="close-teach" @click="onCloseTeach"></div>
                <TeachPlanC widthMode="part" ref="TeachPlanCRef" @onSelectCourseware="onSelectCourseware" />
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.page-wrong-list {
    height: 100%;
    display: flex;
    font-weight: 700;

    .wrong-list-box {
        flex-shrink: 0;
        width: 228px;
        background-color: #fff;
        border-radius: 16px;
        padding: 20px 0;
        /* &.wide {
            width: 410px;
        } */
    }
    .question-teach-box {
        flex: 1;
        display: flex;

        .question-box {
            margin-left: 15px;
            flex: 1;
            background-color: #fff;
            border-radius: 16px;
            box-sizing: border-box;
        }
        .teach-box {
            margin-left: 15px;
            width: 524px;
            background-color: #fff;
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            position: relative;
            .close-teach {
                position: absolute;
                z-index: 1;
                top: 15px;
                right: 15px;
                width: 24px;
                height: 24px;
                background: url(../../assets/<EMAIL>) no-repeat center center/cover;
            }
            .teach-title {
                flex-shrink: 0;
                color: #000;
                font-size: 16px;
                font-weight: bold;
                text-align: left;
                position: relative;
                margin-bottom: 8px;
                &::before {
                    content: '';
                    width: 4px;
                    height: 16px;
                    background: #04a5ff;
                    border-radius: 0px 2px 2px 0px;
                    position: absolute;
                    left: 0px;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
            :deep(.carousel-box) {
                width: 100% !important;
            }
        }
    }
}
</style>
