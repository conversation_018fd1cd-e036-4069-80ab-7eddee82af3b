
import { computed, ref } from 'vue';
import {throttle} from 'lodash-es'

let scale = ref(1)
const targetWidth = ref(1280)
const targetHeight = ref(720)
const scaleStyle = computed(() => {
 return  {transform: `scale(${scale.value})`, width: `${targetWidth.value}px`, height: `${targetHeight.value}px` }
})

function calcScale() {
  const {clientWidth, clientHeight} = document.documentElement
  const widthRatio = clientWidth/targetWidth.value
  const widthHeight = clientHeight/targetHeight.value
  scale.value = Math.min(widthRatio, widthHeight)
}

const handleResize = throttle(calcScale, 60);
window.addEventListener('resize', handleResize);

export default function () {
  calcScale()
  return {
    scaleStyle,
    scale,
    targetWidth,
    targetHeight
  };
};
