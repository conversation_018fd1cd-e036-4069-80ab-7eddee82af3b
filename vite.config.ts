import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  base: "/personal-training-management",
  plugins: [vue()],
  resolve: {
    alias: {
      "@": "/src",
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        math: "always",
        globalVars: {
          mainColor: "red",
        },
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5558
  }
})
