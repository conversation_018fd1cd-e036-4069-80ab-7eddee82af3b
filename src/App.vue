<script setup lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { ref } from 'vue';
import { MessageTypeEnum,postMessage } from '@/common/message';

let zh_CN = ref(zhCN);

window.addEventListener('keydown', (event: KeyboardEvent) => {
    postMessage(MessageTypeEnum.keyBoard, {
        data: {
            key: event.key,
            code: event.code,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            altKey: event.altKey,
            metaKey: event.metaKey,
        },
    });
});

</script>

<template>
    <a-style-provider hash-priority="high">
        <a-config-provider :locale="zh_CN">
            <div class="app-box">
                <router-view />
            </div>
        </a-config-provider>
    </a-style-provider>
</template>

<style lang="less">
.app-box {
    height: 100%;
}
.cp-table .ant-table-cell .ant-btn {
    padding: 0;
    margin-right: 10px;
    height: auto;
    border: 0;
}

:root {
    color: #213547;
    background-color: #ffffff;
}
.ant-modal-mask {
    width: 100%;
}
.ant-modal-wrap {
    width: 100%;
}
.mb10 {
    margin-bottom: 10px !important;
}
</style>
