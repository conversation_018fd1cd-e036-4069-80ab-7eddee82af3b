<script setup lang="ts">
import { getTopicDetail } from '@/api/question';
import { questionFn, QuestionMap } from '@/common/question';
import { computed, onMounted, ref } from 'vue';
import { message } from 'ant-design-vue';
import { getExamQidList, submitExamStore } from '@/api/exam';
import { useRoute } from 'vue-router';

const route = useRoute();

const { contentId } = route.query;

let questionIdList: number[] = [];

const questionList = ref<QuestionMap[]>([]);

const currentQuestionIndex = ref(0);

const questionItem = computed(() => {
    return questionList.value[currentQuestionIndex.value] || {};
});

const result = computed(() => {
    let rightqIds: number[] = [];
    let errorqIds: number[] = [];
    let answers: number[] = [];

    questionList.value.forEach((item) => {
        if (item.isCorrent === 2) {
            rightqIds.push(item.qId);
        }
        if (item.isCorrent === 3) {
            errorqIds.push(item.qId);
        }

        answers.push(item.getUserAnser());
    });

    return {
        rightqIds,
        errorqIds,
        answers,
    };
});

const showResult = computed(() => {
    return result.value.rightqIds.length + result.value.errorqIds.length === questionIdList.length;
});

const getQuestionDetail = async () => {
    let saveExamInfo = JSON.parse(localStorage.getItem(`examInfo-${route.params.courseId}`) || '{}');
    if (saveExamInfo.questionList?.length) {
        questionList.value = saveExamInfo.questionList.map((item: any) => {
            return questionFn(item, true);
        });

        currentQuestionIndex.value = Math.min(result.value.rightqIds.length + result.value.errorqIds.length, questionIdList.length - 1);
    } else {
        questionList.value = await getTopicDetail({ qIdList: questionIdList });
    }
};

const getQuestionList = async () => {
    questionIdList = await getExamQidList({ contentId: contentId as string });
    if (questionIdList.length) {
        getQuestionDetail();
    } else {
        message.error('暂时没有考试题目');
    }
};

onMounted(() => {
    getQuestionList();
});

const nextQuestion = () => {
    currentQuestionIndex.value = (currentQuestionIndex.value as number) + 1;
};

const onSubmitExam = () => {
    submitExamStore({
        examineEncodeId: contentId as string,
        errorqIds: result.value.errorqIds.join(','),
        rightqIds: result.value.rightqIds.join(','),
        answers: result.value.answers.join(','),
    });
};

const finishedSelectAnswer = (selectRight: boolean) => {
    message.info(selectRight ? '回答正确' : '回答错误', 2);
    // 存储题目
    localStorage.setItem(
        `examInfo-${route.params.courseId}`,
        JSON.stringify({
            questionList: questionList.value,
        })
    );

    if (currentQuestionIndex.value === questionList.value.length - 1) {
        onSubmitExam();
        return;
    }

    setTimeout(() => {
        nextQuestion();
    }, 2000);
};

const onSelectAnswer = (showKey: string, value: string, correct: number) => {
    const userSelect = {
        showKey,
        value,
        correct,
    };

    // 作答过的题目或者展示答案的时候不能作答
    if (questionItem.value.isCorrent !== 1) {
        return false;
    }

    // 单选题现在就计算答案(多选就return);
    if (questionItem.value.optionType <= 1) {
        // 将选择的结果赋值给当前数组对应的数据
        questionItem.value.userSelect = {
            [userSelect.showKey]: userSelect,
        };
    } else {
        if (questionItem.value.userSelect[userSelect.showKey]) {
            // 多选的时候如果多次点击相同一个选项，第二次点击就是取消
            delete questionItem.value.userSelect[userSelect.showKey];
        } else {
            questionItem.value.userSelect[userSelect.showKey] = userSelect;
        }
    }
};

const onCompareAnswer = () => {
    const userSelect = questionItem.value.userSelect;
    let selectRight = questionItem.value.StrCorrect === Object.keys(questionItem.value.userSelect).sort().join(',');

    if (questionItem.value.isCorrent !== 1) {
        return false;
    }

    if (questionItem.value.optionType <= 1) {
        // 单选题
        if (Object.keys(userSelect).length !== 1) {
            message.info('请至少选择一个选项');
            return false;
        }
        questionItem.value.isCorrent = selectRight ? 2 : 3;
    } else {
        // 多选题
        if (Object.keys(userSelect).length < 2) {
            message.info('请选择2个及以上答案');
            return false;
        }

        questionItem.value.isCorrent = selectRight ? 2 : 3;
    }

    finishedSelectAnswer(selectRight);
};

const onNext = () => {
    onCompareAnswer();
};
</script>

<template>
    <div class="practice">
        <div class="title">
            随堂测验 <span v-if="questionList.length">{{ currentQuestionIndex + 1 }}/{{ questionList.length }}</span>
        </div>
        <template v-if="questionItem.qId && !showResult">
            <div class="swiper-slide" :data-qId="questionItem.qId">
                <div class="top">
                    <span class="topic-type">{{ questionItem.showOptionType }}</span>
                    <span class="topic-title">{{ questionItem.title }}</span>
                </div>

                <div class="show" v-if="questionItem.mediaType !== 0">
                    <a-image :width="300" v-if="questionItem.mediaType === 1" :src="questionItem.mediaContent" mode="heightFix" />
                    <video v-else id="video" :src="questionItem.mediaContent" autoplay controls></video>
                </div>

                <div class="select-answer">
                    <div class="answer-list">
                        <template v-for="item in questionItem.options">
                            <!-- 只要选中并且不是背题模式并且没有做题就给类名select  只要答案是正确的并且做题了并且选择答案的选项没有就给类名rightSelect，如果题目做了并且选的当前选项是错误的并且不是背题模式就给类名showWrong，答案是正确的并且展示答案或者选择的选项中有这个 -->
                            <!-- 只要是背题模式就肯定是showRight -->
                            <div
                                v-if="item.showKey"
                                :class="{
                                    select: questionItem.userSelect[item.showKey] && questionItem.isCorrent == 1,
                                    rightSelect: item.correct && questionItem.isCorrent != 1 && !questionItem.userSelect[item.showKey],
                                    showWrong: questionItem.userSelect[item.showKey] && questionItem.isCorrent != 1 && !item.correct,
                                    showRight: item.correct && questionItem.userSelect[item.showKey] && questionItem.isCorrent != 1,
                                }"
                                class="answer-item"
                                @click="onSelectAnswer(item.showKey, item.value, item.correct ? 1 : 0)"
                            >
                                <span class="item-icon">{{ item.showKey }}</span>
                                <span class="text">{{ item.value }}</span>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div class="prev-next-box">
                <div @click="onNext" class="next btn">下一题</div>
            </div>
        </template>
        <template v-if="showResult">
            <div class="result">
                <div class="img-box"></div>
                <div class="dec"></div>
                <div class="total">
                    <div class="wrong">答错题数：{{ result.errorqIds.length }}题</div>
                    <div class="right">答对题数：{{ result.rightqIds.length }}题</div>
                </div>
            </div>
        </template>
    </div>
</template>

<style lang="less" scoped>
.practice {
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: #fff;

    .title {
        flex-shrink: 0;
        color: #000;
        font-size: 16px;
        font-weight: bold;
        text-align: left;
        position: relative;
        margin-bottom: 8px;
        &::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #04a5ff;
            border-radius: 0px 2px 2px 0px;
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
        }
        span {
            font-weight: 400;
            font-size: 13px;
            color: rgba(0, 0, 0, 0.65);
        }
    }

    .swiper-slide {
        flex: 1;
        overflow: auto;
        text-align: left;
        .image-box {
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
        }

        img {
            width: auto;
            max-width: 100%;
            max-height: 200px;
        }

        .top {
            .topic-type {
                display: inline-block;
                height: 18px;
                line-height: 18px;
                text-align: center;
                background-color: #1dacf9;
                color: white;
                font-size: 10px;
                border-radius: 5px 5px 5px 0;
                padding: 0 8px;
                vertical-align: top;
                margin-right: 2px;
                margin-top: 4px;
                transform: scale(0.9);
            }

            .topic-title {
                font-size: 18px;
                line-height: 25px;
                color: #333;
            }
        }

        .show {
            margin-top: 15px;
            padding: 0 15px;
            box-sizing: border-box;
            text-align: center;

            image,
            video {
                max-width: 100%;
            }
        }

        .select-answer {
            margin-top: 13px;

            .answer-list {
                .answer-item {
                    cursor: pointer;
                    box-sizing: border-box;
                    min-height: 52px;
                    padding: 7px 15px;
                    display: flex;
                    align-items: center;
                    color: #333;

                    .item-icon {
                        width: 28px;
                        height: 28px;
                        box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.15);
                        border: 1px solid rgba(0, 0, 0, 0.05);
                        border-radius: 50%;
                        font-size: 16px;
                        text-align: center;
                        line-height: 28px;
                        margin-right: 15px;
                        flex-shrink: 0;
                    }

                    .text {
                        font-size: 18px;
                    }

                    /* 多选选中的选项 */
                    &.select {
                        .item-icon {
                            border: none;
                            background-color: #b0b7c6;
                            color: white;
                        }
                    }

                    /* 正确的选项 */
                    &.rightSelect:not(.select) {
                        color: #1dacf9;

                        .item-icon {
                            background-color: #1dacf9;
                            color: white;
                        }
                    }

                    /* 答案选择错误 */
                    &.showWrong {
                        .item-icon {
                            position: relative;

                            &:after {
                                content: '';
                                position: absolute;
                                width: 38px;
                                height: 38px;
                                background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-wrong.png) no-repeat center center/cover;
                                top: -5px;
                                left: -5px;
                            }
                        }

                        .text {
                            color: #ff4a40;
                        }
                    }

                    /* 答案选择正确 */
                    &.showRight {
                        .item-icon {
                            position: relative;

                            &:after {
                                content: '';
                                position: absolute;
                                width: 38px;
                                height: 38px;
                                background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-right.png) no-repeat center center/cover;
                                top: -5px;
                                left: -5px;
                            }
                        }

                        .text {
                            color: #1dacf9 !important;
                        }
                    }
                }

                .submit-answer {
                    height: 44px;
                    margin: 72px auto 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(90deg, rgba(0, 224, 229, 1) 0%, rgba(0, 134, 250, 1) 100%);
                    border-radius: 22px;
                    color: white;
                    font-size: 17px;
                }
            }
        }
    }

    .prev-next-box {
        margin-top: 20px;
        flex-shrink: 0;
        height: 48px;
        background-color: #04a5ff;
        border-radius: 8px;
        .btn {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 16px;
            cursor: pointer;
            &.no-event {
                pointer-events: none;
                color: rgba(4, 165, 255, 0.4);
            }
        }
    }

    .result {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 30px;
        .img-box {
            margin-top: 50px;
            width: 120px;
            height: 100px;
            background: url(./images/<EMAIL>) no-repeat center center/cover;
        }
        .dec {
            margin-top: 14px;
            font-weight: bold;
            font-size: 22px;
            color: rgba(0, 0, 0, 0.85);
        }
        .total {
            margin-top: 30px;
            width: 100%;
            height: 154px;
            background: #eff9ff;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .wrong {
                padding-left: 50px;
                height: 36px;
                line-height: 36px;
                background: url(./images/<EMAIL>) no-repeat top left/36px 36px;
            }
            .right {
                margin-top: 10px;
                padding-left: 50px;
                height: 36px;
                line-height: 36px;
                background: url(./images/<EMAIL>) no-repeat top left/36px 36px;
            }
        }
    }
}
</style>
