<template>
    <div class="teach-setout-page">
        <div class="head">
            <div></div>
            <div>
                {{ info?.studentName }}-{{ info?.subject }} <span class="status-box">{{ statusMap[info?.status as statusEnum] }}</span>
            </div>
            <a-dropdown :trigger="['click']">
                <a-avatar :src="avatarUrl" />
                <template #overlay>
                    <a-menu>
                        <a-menu-item @click="logout">
                            退出
                        </a-menu-item>
                    </a-menu>
                </template>
            </a-dropdown>
        </div>
        <div class="content-box">
            <div class="center-box">
                <div class="l-box">
                    <template v-if="info?.status === statusEnum.End">
                        <div class="outTime-dec">课程已结束</div>
                    </template>
                    <template v-else-if="outTime">
                        <template v-if="outTime < 30 || info?.status === statusEnum.Loding">
                            <div class="outTime-dec">
                                课程已开始，您已迟到 <span class="strong">{{ formatNumber(outTime) }}</span> 分钟
                            </div>
                            <div class="sign-dec">请尽快开始上课！</div>
                        </template>
                        <template v-else>
                            <div class="outTime-dec">课程已过期，请联系主管老师重新排课</div>
                        </template>
                    </template>
                    <template v-else-if="info?.status === statusEnum.For_Begin">
                        <div class="time-box">
                            <div class="time-item-box time1" v-if="+showTime[0] > 0">{{ showTime[0] }}天</div>
                            <div class="time-item-box time2">{{ showTime[1] }}</div>
                            :
                            <div class="time-item-box time3">{{ showTime[2] }}</div>
                            :
                            <div class="time-item-box time4">{{ showTime[3] }}</div>
                            &nbsp;
                            <span class="end-dec">后课程开始</span>
                        </div>
                        <div class="dec">上课时间：{{ dateFormat(info?.beginTime, 'yyyy-MM-dd') }} {{ dateFormat(info?.beginTime, 'hh:mm:ss') }} ~ {{ dateFormat(info?.endTime, 'hh:mm:ss') }}</div>
                    </template>
                    <div class="active-box">
                        <div class="active-item" :class="{ active: info?.status === statusEnum.Loding || (remainTime < 2 * 3600 && outTime < 30 && info?.status !== statusEnum.End) }" @click="onGoMeeting">进入会议室</div>
                        <div class="active-item" :class="{ active: hasPlan }" @click="onGoTeach">查看电子教案</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getLatestCourseStore, getListPlanContent, getRoomInfo } from '@/api/teach';
import { showClock, dateFormat, formatNumber } from '@/common/utils';
import { computed, onMounted, ref } from 'vue';
import { useRoute,useRouter } from 'vue-router';
const router = useRouter();
const route = useRoute();

enum statusEnum {
    For_Begin = 1,
    Loding = 2,
    End = 3,
}

const statusMap = {
    1: '即将开始',
    2: '上课中',
    3: '已结束',
};
const info = ref();
const remainTime = ref<number>(0);
const outTime = ref<number>(0);
const hasPlan = ref(false)
const avatarUrl = ref(JSON.parse(localStorage.getItem('mucang_userInfo') || '{}')?.avatar || '');

const showTime = computed(() => {
    const day = Math.floor(remainTime.value / (24 * 3600));
    const time = remainTime.value % (24 * 3600);
    const str = day + ':' + showClock(time, 'hh:mm:ss');

    return str.split(':');
});
const logout = () => {
    localStorage.removeItem('mucang_userInfo');
    setTimeout(() => {
        location.replace(`${location.origin}${location.pathname}?${location.hash.replace(/(\#(\/\d+)?\/).+/, '$1login')}`);
    }, 1000)
}
const getInfo = () => {
    return getLatestCourseStore({
        courseId: +route.params.courseId,
    }).then(async (data) => {
        try {
            const {status} = await getRoomInfo({roomNo: data.courseNo})
            data.status = status || 1
        } catch (error) {
            data.status = 1
        }
        info.value = data;
        remainTime.value = Math.floor((data.beginTime - data.serverTime) / 1000);
        if (remainTime.value < 0) {
            outTime.value = Math.ceil((data.serverTime - data.beginTime) / (1000 * 60));
        }
    });
};
let timer: ReturnType<typeof setInterval>;
onMounted(() => {
    getInfo().then(() => {
        if (info.value.status === statusEnum.For_Begin) {
            timer = setInterval(() => {
                if (remainTime.value === 0) {
                    location.reload();
                    clearInterval(timer);
                    return;
                }
                remainTime.value--;
            }, 1000);
        }
    });
    
    getListPlanContent({ courseId: +route.params.courseId! }).then((data) => {
        hasPlan.value = data.itemList?.length > 0
    });
});

const onGoMeeting = () => {
    const isProd = import.meta.env.MODE === 'production'
    const host = isProd ? 'https://laofuzi.kakamobi.com/' : 'https://laofuzi.ttt.kakamobi.com/'
    const url = `${host}personal-training-live/?roomNo=${info.value.courseNo}&courseId=${info.value.id}`
    location.href = url
};

const onGoTeach = () => {
    router.push({
        name: 'home'
    });
};
</script>
<style lang="less" scoped>
.teach-setout-page {
    height: 100%;
    padding-bottom: 30px;
    background: linear-gradient(180deg, #0e0c17, #12132e);
    display: flex;
    flex-direction: column;
    .head {
        height: 72px;
        background-color: #1d224c;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;

        .status-box {
            opacity: 0.5;
            border: 1px solid #9fa3b2;
            border-radius: 4px;
            color: #9fa3b2;
            font-size: 15px;
            padding: 0px 5px;
            display: inline-block;
            margin-left: 10px;
        }
    }
    .content-box {
        width: 1200px;
        flex: 1;
        padding: 30px;
        box-sizing: border-box;
        margin: 0 auto;
        overflow: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        .center-box {
            width: 846px;
            height: 305px;
            background: url(./images/1.png) no-repeat center right/455px 305px;

            .l-box {
                width: 460px;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .active-box {
                    margin-top: 56px;
                    display: flex;
                    justify-content: space-between;
                    .active-item {
                        width: 215px;
                        height: 48px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #273352;
                        color: rgba(255, 255, 255, 0.5);
                        cursor: pointer;
                        pointer-events: none;
                        &.active {
                            pointer-events: all;
                            background-color: #0087fa;
                            color: white;
                        }
                    }
                }
            }
            .time-box {
                display: flex;
                justify-content: center;
                align-items: center;
                color: #fefeff;

                .time-item-box {
                    padding: 4px 2px;
                    font-size: 30px;
                    line-height: 42px;
                    background: #273352;
                    border-radius: 4px;
                    &.time1 {
                        margin-right: 20px;
                    }
                }
                .end-dec {
                    margin-left: 10px;
                    font-size: 28px;
                    align-self: end;
                }
            }
            .dec {
                margin-top: 15px;
                font-size: 16px;
                color: white;
                opacity: 0.7;
            }

            .outTime-dec {
                font-size: 26px;
                color: #fefeff;
                .strong {
                    color: #ff4a40;
                    font-size: 30px;
                    font-weight: bold;
                }
            }
            .sign-dec {
                margin-top: 15px;
                color: #fefeff;
                opacity: 0.7;
                font-size: 16px;
            }
        }
    }
}
</style>
