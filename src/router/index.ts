import { createRouter, createWeb<PERSON>ashHistory } from "vue-router";
import Login from "@/pages/login/index.vue";
import WrongList from "@/pages/wrongList/index.vue";
import Home from "@/pages/home/<USER>";
import Category from "@/pages/category/index.vue";
import teachPlan from "@/pages/teachPlan/index.vue";
import Exam from "@/pages/exam/index.vue";
import ExamTotal from "@/pages/examTotal/index.vue";
import Main from "@/components/Main/index.vue"
import CoursePlan from "@/pages/coursePlan/index.vue";
import TeachSetout from "@/pages/teachSetout/index.vue";

const routes = [
  {
    path: '/:courseId(\\d+)?', redirect: (path: any) => {
      return {
        name: 'login',
        params: {
          courseId: path.params?.courseId
        }
      }
    }
  },
  { name: 'login', path: "/:courseId(\\d+)?/login", component: Login },
  {
    name: 'coursePlan',
    path: '/coursePlan',
    component: CoursePlan
  },
  {
    name: 'teachSetout',
    path: '/teachSetout/:courseId(\\d+)',
    component: TeachSetout
  },
  {
    name: 'main', path: '/:courseId(\\d+)/main', component: Main,
    redirect: () => {
      return {
        name: 'home'
      }
    },
    children: [
      { name: 'home', path: "home", component: Home },
      { name: 'category', path: "category", component: Category },
      { name: 'teachPlan', path: 'teachPlan', component: teachPlan },
      { name: 'wrongList', path: 'wrongList', component: WrongList },
      { name: 'exam', path: 'exam', component: Exam },
      { name: 'examTotal', path: 'examTotal', component: ExamTotal }
    ]
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
