<script setup lang="ts">
import { ref, onMounted, watch, toRaw } from 'vue';
import { storeToRefs } from 'pinia';
import {debounce, assign} from 'lodash-es'
import { WrongListType } from '@/common/const';
import { dateFormat, trackEvent } from '@/common/utils';
import * as echarts from 'echarts';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { getCourseList, getMockExamList, kemuParamsMap } from '@/api/teach';
import userPlanState from '@/store/modules/planState';
import { MessageTypeEnum,postMessage } from '@/common/message';

const router = useRouter();
const route = useRoute();
const planState = userPlanState()
const { examTotalData } = storeToRefs(planState);
let { sno, carType, tutorKemu } = route.query;
carType = carType || 'car'
tutorKemu = tutorKemu || '10'

const chartRef = ref(null)

let examList = ref([])
let ExamListContentRef = ref<HTMLElement>()
let myChart: any
const paging = ref({
    limit: 15,
    total: 0
})
let userScrolling = ref(false);

let index = 0;
let isExternalUpdate = false;

function getMockAllList(page: number, limit: number) {
    const params: any = {
        sno,
        carType,
        ...kemuParamsMap[tutorKemu],
        page,
        limit
    }

    if (examTotalData.value.efficientExam) {
        params.effectiveMockExam = true
    }

    return getMockExamList(params)
}
function getScatterData(courseList: any[], examList: any[]) {
    const reverseExamList = JSON.parse(JSON.stringify(examList)).reverse();
    const scatterData: any[] = [];
    const tempObj: any = {};

    courseList.forEach((ele: any, index: number) => {
        if (index === 0) {
            ele.label = '首次上课';
        } else {
            ele.label = '上课';
        }
    });

    reverseExamList.forEach((item: any, index: number) => {
        // 从第二个点开始算
        if (index === 0) {
            return;
        }
        let preItem = reverseExamList[index - 1]
        let startTime = preItem.examTime || 0;
        let endTime = item.examTime;
        
        courseList.forEach((ele: any) => {
            const eleDay = dateFormat(ele.beginTime, 'yyyy-MM-dd')

            if (!!tempObj[eleDay]) {
                return;
            }
            if (ele.beginTime < reverseExamList[0].examTime || ele.beginTime > reverseExamList[reverseExamList.length - 1].examTime) {
                return;
            }

            if (startTime <= ele.beginTime && ele.beginTime < endTime) {
                let value = [];
                // 测试字段
                let test = {};
                if (eleDay === dateFormat(startTime, 'yyyy-MM-dd')) {
                    value = [index, preItem.score]
                    test = {
                        point: '点---------'
                    }
                } else {
                    const ratio = (ele.beginTime - startTime) / (endTime - startTime);
                    const startScore = preItem.score || 0;
                    const endScore = item.score;

                    value = [index + ratio * 1, startScore + ratio * (endScore - startScore)]
                    test = {
                        ratio,
                        startScore,
                        endScore
                    }
                }

                tempObj[eleDay] = {
                    name: ele.label,
                    itemStyle: {
                        color: ele.label === '首次上课' ? '#FF4A40' : '#04A5FF'
                    },
                    value,
                    test
                }
            }
        });

        scatterData.push({
            value: [index + 1, 50]
        })
    })

    for (const key in tempObj) {
        scatterData.push({
            ...tempObj[key],
            xAxisTime: key
        })
    }

    return scatterData
}
function getStudyTime() {
    return getCourseList({
        sno: sno as string,
        carType: carType as string,
        tutorKemu: tutorKemu as string,
    })
}

async function renderCharts() {
    const {itemList, total} = await getMockAllList(1, 30)
    if (examTotalData.value.page === 1) {
        paging.value.total = total
        examList.value = itemList.slice(0, paging.value.limit)
    }
    const {itemList: courseList} = await getStudyTime()
    const scatterData = getScatterData(courseList, itemList)
    let chartDom = chartRef.value;
    if (!myChart) {
        myChart = echarts.init(chartDom);
    }
    const reverseExamList = JSON.parse(JSON.stringify(itemList)).reverse();
    let scoreList: any[] = reverseExamList.map((item: any) => item.score)
    const option = {
        grid: {
            top: 40,
            right: 15,
            bottom: 40
        },
        xAxis: [{
            min: 0,
            max: reverseExamList.length + 1,
            show: false
        }, {
            min: 0,
            max: reverseExamList.length + 1,
            show: false
        }],
        yAxis: {
            type: 'value',
            min: 0,
            max: 100,
            data: [0, 20, 40, 60, 80, 100]
        },
        series: [{
            type: 'scatter',
            symbolSize: 8,
            label: {
                show: true,
                overflow: 'breakAll',
                position: 'top',
                formatter: (params: any) => {
                    if (params.name === '首次上课') {
                        return `{firstlesson|${params.name}(${dateFormat(params.data.xAxisTime, 'MM/dd')})}`
                    }
                    return `{lesson|${params.name}(${dateFormat(params.data.xAxisTime, 'MM/dd')})}`
                },
                rich: {
                    lesson: {
                        backgroundColor: 'rgba(4,165,255,0.85)',
                        padding: [4, 5, 2, 5],
                        color: '#fff'
                    },
                    firstlesson: {
                        backgroundColor: 'rgba(255,74,64,0.85)',
                        padding: [4, 5, 2, 5],
                        color: '#fff'
                    }
                },
            },
            symbol: (value: any, params: any) => {
                if (!params.data.xAxisTime) {
                    return 'none';
                }
            },
            data: scatterData
        }, {
            data: scoreList.map((item, index) => [index + 1, item]),
            xAxisIndex: 1,
            type: 'line',
            // symbol: 'none',
            lineStyle: {
                color: '#04A5FF'
            },
            markLine: {
                silent: true,
                lineStyle: {
                    color: '#04A5FF',
                    cap: 'round'
                },
                data: [{
                    yAxis: 90
                }],
                label: {
                    position: 'insideEndBottom',
                    formatter: '及格线90分',
                    color: '#999'
                },
            },
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(0,134,250,0.2)'
                }, {
                    offset: 1,
                    color: 'rgba(255, 255, 255, 0.01)'
                }])
            }
        }]
    };
    myChart.setOption(option);
}

onMounted(async () => {
    renderCharts()

    trackEvent({
        fragmentName1: '模拟考试总览',
        actionType: '触发',
        actionName: '页面初始化',
        pageUrl: location.href,
    })
})
watch(() => examTotalData.value.efficientExam, () => {
    examTotalData.value.page = 1
    renderCharts()
})
watch(() => examTotalData.value.page, async (page) => {
    const res = await getMockAllList(page, paging.value.limit)
    paging.value.total = res.total
    examList.value = res.itemList
})

onBeforeRouteLeave((to, from, next) => {
    // 使用 router.resolve 方法来获取完整的URL
    const fullUrl = window.location.origin + location.pathname + location.search + '#' + to.fullPath;

    // 打印即将跳转的完整URL
    console.log('即将跳转的完整URL:', fullUrl);

    postMessage(MessageTypeEnum.OpenExam, {
        data: {
            url: fullUrl,
        },
    });

    // 继续路由导航
    next();
});

const onGoWrongList = async (type = WrongListType.Exam, uniqueId: string) => {
    router.push({
        name: 'wrongList',
        query: {
            sno,
            wrongListType: type,
            uniqueId,
            name: '考试错题'
        },
    });
};

const examListScroll = debounce((e) => {
    if (userScrolling.value) {
        const scrollTop = e.srcElement.scrollTop
        examTotalData.value.examListScrollTop = scrollTop
    }
}, 16)

const setScrollTop = async (scrollTop: any) => {
    const content = ExamListContentRef?.value as HTMLElement
    if (content.scrollTo) {
        content.scrollTo({
            top: scrollTop,
            behavior: "smooth",
        });
    } else {
        content.scrollTop = scrollTop
    }
};

watch(examTotalData, () => {
    if (!isExternalUpdate) {
        sendTIWH5WebCtrlMessage();
    }
}, {
    deep: true
})

watch(() => examTotalData.value.examListScrollTop, (val) => {
    setScrollTop(val)
})

window.TIWH5WebCtrl.on(
    'TIW_H5WEB_DATA',
    async (data: any) => {
        console.log('接受：', data, index);
        index++;
        isExternalUpdate = true
        assign(examTotalData.value, data.examTotalData)
        setTimeout(() => { isExternalUpdate = false; }, 0);
    }
);

const sendTIWH5WebCtrlMessage = () => {
    const params: any = {
        examTotalData: toRaw(examTotalData.value),
    };

    console.log('send params:', +new Date(), params);
    window.TIWH5WebCtrl.syncData(params);

    trackEvent({
        fragmentName1: '模拟考试总览',
        actionType: '触发',
        actionName: '信令同步',
        pageUrl: location.href,
        syncData: params,
    })
}
</script>

<template>
    <div class="page-exam-total">
        <div style="padding-top: 6px">
            <a-checkbox v-model:checked="examTotalData.efficientExam">只看有效成绩</a-checkbox>
        </div>
        <div class="content">
            <div class="chart" ref="chartRef"></div>
            <div class="list">
                <div class="table">
                    <div class="thead">
                        <div class="tr">
                            <div class="th sort">序号</div>
                            <div class="th time">模考时间</div>
                            <div class="th useTime">耗时</div>
                            <div class="th score">分数</div>
                            <div class="th look-wrong">错题</div>
                        </div>
                    </div>
                    <div class="tbody">
                        <div @scroll="examListScroll" v-onUserScroll="(val) => userScrolling = val" ref="ExamListContentRef">
                            <div class="tr" v-for="(item, index) in examList">
                                <div class="td sort">{{index + 1 + (((examTotalData.page - 1)) * paging.limit)}}</div>
                                <div class="td time">{{item.showExamTime}}</div>
                                <div class="td useTime">{{item.showPeriod}}</div>
                                <div class="td score" :class="item.score >= 90 ?'pass':'fail'">{{item.score}}</div>
                                <div class="td look-wrong" @click="onGoWrongList(WrongListType.Exam, item.uniqueId)">查看</div>
                            </div>
                        </div>
                    </div>
                </div>
                <a-pagination v-model:current="examTotalData.page" :total="paging.total" :pageSize="paging.limit" :showSizeChanger="false" size="small" style="text-align: right; padding-top: 10px;">
                </a-pagination>
            </div>
        </div>
    </div>
    
</template>

<style lang="less" scoped>
.page-exam-total {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    font-weight: 700;
    color: #213547;
    .content {
        flex: 1;
        display: flex;
        height: calc(100% - 32px);
    }
    .chart {
      width: 60%;
        height: 100%;
    }
    .list {
        width: 40%;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 20px;
    }
    
    .table {
        flex: 1;
        overflow: auto;
        display: flex;
        flex-direction: column;

        .sort {
            width: 50px;
        }

        .time {
            width: 160px;
        }

        .useTime {
            width: 100px;
        }

        .score {
            width: 60px;

            &.pass {
                color: #04A5FF;
            }

            &.fail {
                color: #FF4A40;
            }
        }

        .look-wrong {
            width: 75px;
            cursor: pointer;
        }

        .th,
        .td {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .thead {
            background-color: #EBF8FF;
            flex-shrink: 0;

            .tr {
                height: 40px;
                display: flex;
                font-size: 15px;
                font-weight: bold;
            }
        }

        .tbody {
            flex: 1;
            height: 0;
            > div {
              height: 100%;
              overflow: auto;
            }

            .tr {
                height: 40px;
                display: flex;
                font-size: 14px;

                &:nth-child(2n + 2) {
                    background-color: #F5FBFF;
                }

                .look-wrong {
                    color: #04A5FF;
                }
            }
        }

    }
}
</style>
