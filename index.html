<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <script src="https://cstaticdun.126.net/load.min.js?t=201903281201"></script>
  <script>function _classCallCheck(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||false;a.configurable=true;if("value"in a)a.writable=true;Object.defineProperty(e,a.key,a)}}function _createClass(e,t,n){if(t)_defineProperties(e.prototype,t);if(n)_defineProperties(e,n);Object.defineProperty(e,"prototype",{writable:false});return e}var TEduBoardH5WebEvent={TIW_H5WEB_SYNC:"TIW_H5WEB_SYNC",TIW_H5WEB_ACK:"TIW_H5WEB_ACK",TIW_H5WEB_DATA:"TIW_H5WEB_DATA",TIW_H5WEB_PERMISSION:"TIW_H5WEB_PERMISSION",TIW_H5WEB_SNAPSHOT:"TIW_H5WEB_SNAPSHOT"};function TiwH5WebEvent(){}TiwH5WebEvent.prototype.on=function(e,t){this._cbs=this._cbs||{};(this._cbs[e]||(this._cbs[e]=[])).unshift(t);return this};TiwH5WebEvent.prototype.off=function(e,t){this._cbs=this._cbs||{};if(!e)return this._cbs={};if(!t)return delete this._cbs[e];var n=this._cbs[e]||[];var a;while(n&&~(a=n.indexOf(t))){n.splice(a,1)}return this};TiwH5WebEvent.prototype.fire=function(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:this;var t=arguments.length>1?arguments[1]:undefined;this._cbs=this._cbs||{};var n=this._cbs[t];if(n){var a=n.length;for(var i=arguments.length,s=new Array(i>2?i-2:0),r=2;r<i;r++){s[r-2]=arguments[r]}while(a--){n[a].apply(e,[].concat(s))}}return this};var H5WebCtrl=function(){"use strict";function e(){_classCallCheck(this,e);this.jsStatus={};this.version="202303241903";this.module="TIW_H5WEB";this.event=new TiwH5WebEvent;try{this.keys=JSON.parse(window.name)}catch(e){this.keys={}}var t=this;window.addEventListener("message",function(e){if(e.data){var n=e.data.cmd;var a=e.data.data;var i=e.data.module;if(n&&a&&i&&t.module==i){switch(n){case TEduBoardH5WebEvent.TIW_H5WEB_PERMISSION:t.event.fire(t,n,a);break;case TEduBoardH5WebEvent.TIW_H5WEB_DATA:t.event.fire(t,n,a);break;case TEduBoardH5WebEvent.TIW_H5WEB_SYNC:t.postAck(a);break;case TEduBoardH5WebEvent.TIW_H5WEB_SNAPSHOT:t.postSnapshot(a);break;default:break}}}})}_createClass(e,[{key:"version",value:function e(){return this.version}},{key:"on",value:function e(t,n){this.event.on(t,n)}},{key:"off",value:function e(t,n){this.event.off(t,n)}},{key:"syncData",value:function e(t){if(window.parent&&window.parent!==window){window.parent.postMessage({module:this.module,id:this.keys.id,cmd:TEduBoardH5WebEvent.TIW_H5WEB_DATA,data:t},"*")}}},{key:"postSnapshot",value:function e(t){var n="https://res.qcloudtiw.com/board/third/html2canvas/html2canvas.min.js";var a=this;this.loadJS(n,function(){if(window.html2canvas){var e=document.getElementsByClassName("none mobile webkit")[0];e&&e.classList.remove("none");window.html2canvas(document.body,{allowTaint:true,useCORS:true,backgroundColor:null,logging:false}).then(function(n){var i=n.toDataURL();if(window.parent&&window.parent!==window){window.parent.postMessage({module:a.module,id:a.keys.id,cmd:TEduBoardH5WebEvent.TIW_H5WEB_SNAPSHOT,data:{src:t.src,dataUrl:i}},"*")}e&&e.classList.add("none")})}})}},{key:"postAck",value:function e(t){if(window.parent&&window.parent!==window){window.parent.postMessage({module:this.module,cmd:TEduBoardH5WebEvent.TIW_H5WEB_ACK,data:t},"*")}}},{key:"loadJS",value:function e(t,n){if(this.jsStatus[t]===true){n()}else{var a=this;var i=document.createElement("script");i.type="text/javascript";if(i.readyState){i.onreadystatechange=function(){if(this.readyState=="loaded"||this.readyState=="complete"){this.onreadystatechange=null;a.jsStatus[t]=true;n()}}}else{i.onload=function(){a.jsStatus[t]=true;n()}}i.setAttribute("src",t);document.getElementsByTagName("head")[0].appendChild(i)}}}]);return e}();window.TIWH5WebCtrl=new H5WebCtrl;</script>
  <title>电子教案</title>
</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>
