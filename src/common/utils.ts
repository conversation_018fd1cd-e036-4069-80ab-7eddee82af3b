import QRCode from 'qrcode'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import { Platform } from "../common/env";
import router from '../router'

/** url签名 */
export function sign(a: number): string {
    const c = Math.abs(parseInt((new Date().getTime() * Math.random() * 10000) + '')).toString();
    let d: any = 0;
    for (let b = 0; b < c.length; b++) {
        d += parseInt(c[b]);
    }
    const e = ((f: any) => {
        return (g: string | any[], h: number) => {
            return ((h - 0 + g.length) <= 0) ? g : (f[h] || (f[h] = Array(h + 1).join('0'))) + g;
        };
    })([]);

    d += c.length;
    d = e(d, 3 - d.toString().length);
    return a.toString() + c + d;
}

/** 时间展示 */
export function dateFormat(dateValue: string | number | Date, formatStr = 'yyyy-MM-dd hh:mm', showDayType = 24) {
    let timestamp = new Date(dateValue);
    let fmt = formatStr;
 
    const week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const o = {
        'M+': timestamp.getMonth() + 1,
        'd+': timestamp.getDate(),
        'h+': timestamp.getHours() % showDayType,
        'H+': timestamp.getHours(),
        'm+': timestamp.getMinutes(),
        's+': timestamp.getSeconds(),
        'q+': Math.floor((timestamp.getMonth() + 3) / 3),
        'S': timestamp.getMilliseconds(),
        'W+': week[timestamp.getDay()]
    };
    let k: keyof typeof o;
    if (!dateValue) {
        return '';
    }

    if (typeof timestamp !== 'object') {
        timestamp = new Date(timestamp);
    }
    fmt = fmt || 'yyyy-MM-dd';

    if ((/(y+)/).test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (timestamp.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            // eslint-disable-next-line eqeqeq
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)) as any);
        }
    }
    return fmt;

}


export const deleteEmpty = (obj: any) => {
    for (const key in obj) {
        if (obj[key] === null || obj[key] === undefined) {
            delete obj[key];
        }
    }
    return obj;
};


/** 模拟toast */
export function makeToast(message: string, foz = '20px', time = 2000) {
    return new Promise((resolve) => {
        if (document.getElementById('myToast')) {
            document.body.removeChild(document.getElementById('myToast') as Node);
        }

        const div: HTMLDivElement = document.createElement('div');
        div.innerText = message;

        div.setAttribute('id', 'myToast');

        div.style.position = 'fixed';
        div.style.left = '50%';
        div.style.top = '50%';
        div.style.transform = 'translate(-50%, -50%)';
        div.style.webkitTransform = 'translate(-50%, -50%)';
        div.style.background = 'rgba(0, 0, 0, 0.7)';
        div.style.zIndex = '9999';
        div.style.padding = '10px 20px';
        div.style.borderRadius = '6px';
        div.style.textAlign = 'center';
        div.style.color = '#ffffff';
        div.style.maxWidth = '90%';
        // div.style.minWidth = '60%';
        div.style.fontSize = foz;
        div.style.lineHeight = '1.5';

        document.body.appendChild(div);
        setTimeout(function () {
            div.remove();
            resolve('');
        }, time);
    });
}

// 定义一个防抖函数类型
type DebounceFunction = (...args: any[]) => any;
// 防抖器函数
export function debounce<F extends DebounceFunction>(func: F, wait: number = 500): F {
    let timeout: ReturnType<typeof setTimeout> | null = null;

    // 返回一个新的函数，这个函数会在指定的延迟后执行原函数
    return function (...args: Parameters<F>) {
        // 如果已经设置过定时器，则清除定时器
        if (timeout !== null) {
            clearTimeout(timeout);
        }

        // 设置一个新的定时器，在指定的延迟后执行原函数
        timeout = setTimeout(() => {
            func.apply(this, args);
        }, wait);
    } as F;
}

export function copyTextToClipboard(text: string): void {
    if (!navigator.clipboard) {
        fallbackCopyTextToClipboard(text);
        return;
    }
    navigator.clipboard.writeText(text).then(() => {
        console.log('复制成功: ' + text);
        makeToast('复制成功');
    }).catch((err) => {
        makeToast('复制失败');
        console.error('复制失败', err);
    });
}

export async function copyURLToQRCodeAndClipboard(text: string): Promise<any> {
    async function copyImageToClipboard(dataURL: any) {
        const clipboardItem = new ClipboardItem({
            [dataURL.match(/^data:(.+?);/)[1]]: await fetch(dataURL).then(r => r.blob())
        });
        // 将剪贴板项复制到剪贴板
        return await navigator.clipboard.write([clipboardItem]);
    }
    return new Promise((resolve, reject) => {
        // const canvas = document.createElement('canvas')
        // canvas.width = 320;
        // canvas.height = 440;
        // let ctx = canvas.getContext("2d")!;
        // ctx.fillStyle = '#ffffff'; 
        // ctx.fillRect(0, 0, canvas.width, canvas.height);
        // ctx.font = "28px 宋体";
        // ctx.fillStyle = '#000000'; 
        // ctx.fillText("识别二维码", 86, 50);
        // ctx.fillText("进入驾考宝典私教课堂", 20, 90);
        QRCode.toDataURL(text, { width: 320 }, function (err: Error, url: any) {
            // const img = document.createElement('img')
            // img.src = url
            // img.onload = function () {
            //     ctx.drawImage(img, 0, 100, 320, 320);
                if (err) {
                    console.error('复制失败', err);
                    reject(err)
                }
                copyImageToClipboard(url).then(
                    function() {
                        console.log('图片已复制到剪贴板');
                        resolve(true)
                    },
                    function(error) {
                        console.error('复制失败', error);
                        reject(error)
                    }
                );
            //   };
        })
    })
}

function fallbackCopyTextToClipboard(text: string): void {
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 防止在移动设备上弹出键盘
    textArea.setAttribute('readonly', '');
    textArea.style.position = 'absolute';
    textArea.style.left = '-9999px';

    document.body.appendChild(textArea);
    textArea.select();
    try {
        const successful = document.execCommand('copy');
        const msg = successful ? '复制成功' : '复制失败';
        console.log(msg);
        makeToast(msg);
    } catch (err) {
        console.error('无法复制', err);
    }

    document.body.removeChild(textArea);
}

export const formatNumber = (n: number) => {
    const s = n.toString();
    return s[1] ? s : '0' + s;
};

export function showClock(time: number, format: string = 'mm:ss') {
    var hh = formatNumber(Math.floor(time / 3600));
    var mm = formatNumber(Math.floor(Math.floor(time % 3600) / 60));
    var ss = formatNumber(Math.floor(time % 60));
    var str = '';
    if (format.indexOf('hh') > -1) {
        str += hh + ':'
    }
    if (format.indexOf('mm') > -1) {
        str += mm + ':'
    }
    if (format.indexOf('ss') > -1) {
        str += ss
    }
    return str;
}

export function errorToObject(error: Error) {
    const { name, message, stack } = error;
    return {
        name,
        message,
        stack: stack?.split('\n').map(line => line.trim())
    };
}

export function sendWarning(pathType: number, playload: any) {
    let pageName = '公共页'
    let fragmentName1 = 'h5页面'
    let actionType = '触发'
    let actionName = '异常上报'

    // 公共页_h5页面_触发异常上报
    stat.trackEvent({
        pageName,
        fragmentName1,
        actionType,
        actionName,
        pageUrl: location.href,
        _pageName: '授课工具教案',
        pathType,
        playload,
        eventId: 'debug',
    })
}

function paramsExtra(params: any) {
    const route = router.currentRoute.value
    return Object.assign({
        pageName: '授课工具教案',
        courseId: route.params.courseId,
    }, params)
}

export function trackEvent(params: any) {
    stat.trackEvent(paramsExtra(params))
}

export async function statInit() {
    let authToken = ''
    if (!Platform.isMuCang) {
        authToken = JSON.parse(localStorage.getItem('mucang_userInfo') || '{}')?.authToken || ''
    }
    stat.init({
        appName: 'jiakaoshouke',
        productCategory: 'jiakaoshouke',
        product: '驾考授课工具',
        joinStr: '_',
        authToken,
    })
}