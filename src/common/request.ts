/*
 * ------------------------------------------------------------------
 * 网络请求
 * ------------------------------------------------------------------
 */

import { MCBaseStore } from '@simplex/simple-base';
import { makeToast, sign, sendWarning } from './utils';

type HostName = 'sirius' | 'misc' | 'activity' | 'config' | 'swallow' | 'jiakao3d' | 'squirrel' | 'cheyouquan' | 'monkey' | 'jiakao' | 'tiku' | 'panda' | 'pony' | 'parrot' | 'rights' | 'login';

interface RequestOptions {
    hostName?: HostName,
    url: string,
    method?: 'GET' | 'POST',
    headers?: Record<string, any>,
    data?: Record<string, any>
    others?: any
    noToast?: boolean
    returnStore?: boolean
    serialize?: Function
}

const signMap: Record<HostName, string> = {
    'sirius': '*#06#c2uXpo9IeKaIkpyJdXipfGxs',
    'misc': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'activity': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'config': '*#06#d3pycm9DSYd6lndDckVwkzyZ',
    'swallow': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'jiakao3d': '*#06#bIWiiqmXc3lspqhJj5NIipya',
    'squirrel': '*#06#iKVuc32KRW12cqg8QnGkdX16',
    'cheyouquan': '',
    'monkey': '*#06#PGuPbJiIkz2PeItsc5qKhItG',
    'jiakao': '',
    'tiku': '*#06#i4mleXFFkIlCqXWal3eCQkN6',
    'panda': '*#06#l5J2nW13l3qEfHdubKKmPJyj',
    'parrot': '*#06#c2uXpo9IeKaIkpyJdXipfGxs',
    'pony': '*#06#bJaWjoOnhaN9pXCCbqiSoqd2',
    'rights': '*#06#R31tjnFuiaJubpKojalHbnmG',
    'login': ''
};

// const resignMap: Record<HostName, string> = {
//     'sirius': 'hello',
//     'misc': 'debug',
//     'activity': 'hello',
//     'config': '',
//     'swallow': 'hello1',
//     'jiakao3d': 'xx',
//     'squirrel': 'helloworld',
//     'cheyouquan': '',
//     'monkey': 'debug',
//     'jiakao': 'debug',
//     'tiku': '_debug',
//     'panda': 'debug',
//     'pony': 'hello',
//     'parrot': 'hello',
//     'rights': 'hello',
//     'login': ''
// };
var door = (new URL(location.href)).searchParams.get('door');

var authTokenFn = function () {
    const authToken = JSON.parse(localStorage.getItem('mucang_userInfo') || '{}')?.authToken || '';
    return door || authToken
}

let isJumpLogin = false;

/** 发起网络请求 */
export function request({ hostName = 'sirius', url, method = 'GET', headers, data = {}, others = {}, noToast, returnStore, serialize }: RequestOptions): Promise<any> {
    /*
     | 处理参数
     */
    let params: any = {};
    const authToken = authTokenFn();

    if (authToken && !data.authToken) {
        data.authToken = authToken;
    }

    data._r = sign(1);

    // 如果要用json形式发请求，参数需要JSON.stringify
    if (others?.ajaxOptions) {

        params = JSON.stringify(data);
    } else {
        params = data;
    }

    const xhr = MCBaseStore.extend({
        url: `${hostName}://${url}`,
        sign: signMap[hostName],
        headers,
        method: method.toLocaleUpperCase(),
        ...others,
        errorToast: false,
        type: 'online'
    }).create()

    const req = xhr.request(params)

    const controller = new AbortController();
    const signal = controller.signal;
    let promise = new Promise((resovle, reject) => {
        if (signal) {
            signal.addEventListener('abort', () => {
                xhr.abort();
                promise = new Promise(() => {})
            });
        }
        req.then((res: any, store: any) => {
            // console.info('[response]', url, res);
            if (serialize) {
                resovle(serialize(returnStore ? store.data : res));
            } else {
                resovle(returnStore ? store.data : res);
            }
        }).fail((_errorCode: number, error: any) => {
            if (signal.aborted) return
            const text = JSON.parse(error.text);
            if (!noToast) {
                makeToast(error.statusText || '');
            }

            if (text.errorCode === 403 && !isJumpLogin) {
                isJumpLogin = true
                localStorage.removeItem('mucang_userInfo');
                setTimeout(() => {
                    location.replace(`${location.origin}${location.pathname}?${location.hash.replace(/(\#(\/\d+)?\/).+/, '$1login')}`);
                }, 1000)
            }
            sendWarning(2, {
                url: `${hostName}://${url}`,
                params,
                content: text
            })
            reject(error);
        });
    });

    return Object.assign(promise, {abort: controller.abort.bind(controller)})
}
