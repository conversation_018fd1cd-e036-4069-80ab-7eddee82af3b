/*
 * ------------------------------------------------------------------
 * 环境上下文相关
 * ------------------------------------------------------------------
 */

const userAgent = navigator.userAgent.toLowerCase();

/** 车型 */
export enum CarType {
    CAR = 'car',
    TRUCK = 'truck',
    BUS = 'bus',
    MOTO = 'moto',
    GUACHE = 'light_trailer',
    // 资格证
    KEYUN = 'keyun',
    HUOYUN = 'huoyun',
    WEIXIAN = 'weixian',
    JIAOLIAN = 'jiaolian',
    CHUZU = 'chuzu',
    WANGYUE = 'wangyue',
    WEIXIAN_YAYUN = 'weixian_yayun',
    CHACHE = 'chache',
    WEIXIAN_ZHUANGXIE = 'weixian_zhuangxie',
    BAOZHA = 'baozha',
    BAOZHA_YAYUN = 'baozha_yayun',
    BAOZHA_ZHUANGXIE = 'baozha_zhuangxie',
    JIAOLIAN_ZAIJIAOYU = 'jiaolian_zaijiaoyu'
}

/** 科目 */
export enum KemuType {
    Ke0 = 0,
    Ke1 = 1,
    Ke2 = 2,
    Ke3 = 3,
    Ke4 = 4
}

/** 平台判断 */
export const Platform = {
    isWeiXin: !!userAgent.match(/MicroMessenger/i),
    isAndroid: userAgent.indexOf('android') > -1,
    isMuCang: userAgent.indexOf('mucang') > -1,
    isIOS: userAgent.indexOf('iphone') > -1 || userAgent.indexOf('ipad') > -1,
}

/** 支付方式 */
export enum PayType {
    Alipay = 1,
    Weixin = 2,
    ApplePay = 3
}

let payType: PayType = PayType.Weixin;

export function setPayType(paramsPayType: PayType) {
    payType = paramsPayType;
}

export function getPayType() {
    return payType;
}

/** 页面名称 */
export let PageName = '未知页';
/** 设置页面名称，请尽早设置 */
export function setPageName(pageName = '未知页') {
    PageName = pageName;
}
export function getPageName() {
    return PageName;
}
