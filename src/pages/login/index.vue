<script setup lang="ts">
import { ref } from 'vue';
import { smsCheck, codeLogin } from '@/api/login';
import { makeToast } from '@/common/utils';
import userStore from '@/store/modules/user';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();
const userInfoStore = userStore();

const phone = ref();
const showCode = ref(false);
const showMask = ref(false);
const smsTimeFlag = ref(false);
const canSubmit = ref(false);
const smsTime = ref(0);
const smsId = ref('');
const smsCode = ref('');
const errorMessage = ref();

const goHome = () => {
    if (route.params.courseId) {
        router.push({
            name: 'home',
        });
    }else{
        router.push({
            name: 'coursePlan',
        });
    }
};

if (/\/\?\#\/.+/g.test(location.href)) {
    location.replace(location.href.replace(/\/\?\#\//, '/#/'));
}

const userInfo = userInfoStore.getUserInfo;
if (userInfo && userInfo.authToken) {
    goHome();
}

const phoneInput = () => {
    if (/^\d{11}$/.test(phone.value)) {
        showCode.value = true;
    } else {
        showCode.value = false;
    }
    // 手机号清空，错误和验证码清空
    if (phone.value.trim().length === 0) {
        clearInterval(codeTimer);
        errorMessage.value = '';
        smsCode.value = '';
        smsTimeFlag.value = false;
    }
};

const smsCodeInput = () => {
    if (/^\d{6}$/.test(smsCode.value)) {
        canSubmit.value = true;
    } else {
        canSubmit.value = false;
    }
};

const getCaptcha = () => {
    showMask.value = true;
    let captchaIns: { refresh: () => void };
    window.initNECaptcha(
        {
            captchaId: '6f92317b6e7d4f4faa77a360d65826c5',
            element: '#captcha',
            mode: 'embed',
            onReady: function (instance: any) {
                // 验证返回函数
                console.log(instance);
            },
            onVerify: function (err: any, data: { validate: any }) {
                if (!err) {
                    if (data && data.validate) {
                        codeCheck(data.validate);
                        setTimeout(function () {
                            showMask.value = false;
                        }, 500);
                    }
                } else {
                    setTimeout(function () {
                        captchaIns.refresh();
                    }, 500);
                }
            },
        },
        function onload(instance: { refresh: () => void }) {
            captchaIns = instance;
            // 初始化成功
        },
        function onerror(err: any) {
            // 验证码初始化失败处理逻辑，例如：提示用户点击按钮重新初始化
            console.log(err);
        }
    );
};

const codeCheck = async (NECaptchaValidate: string) => {
    const res = await smsCheck({
        _appName: 'jiakaoshouke',
        _platform: 'web',
        _authVersion: 1.5,
        phoneNumber: phone.value,
        NECaptchaValidate: NECaptchaValidate,
    });
    // checkType: "TRUE"
    // delay: 0
    // restSeconds: 60
    // smsCode: null
    // smsId: "49439c06affbd5ebfb0644ebfa534236"
    console.log('smsCheck', res);
    smsTime.value = res.restSeconds;
    smsId.value = res.smsId;
    setCodeTimer();
};

let codeTimer: ReturnType<typeof setInterval>;

const setCodeTimer = () => {
    codeTimer = setInterval(() => {
        if (smsTime.value === 1) {
            clearInterval(codeTimer);
            smsTimeFlag.value = false;
            return;
        }
        smsTime.value -= 1;
    }, 1000);

    smsTimeFlag.value = true;
};

const loginSms = async () => {
    if (!canSubmit.value) {
        return;
    }

    if (!smsId.value) {
        makeToast('请获取验证码');
        return;
    }

    if (!smsCode.value) {
        makeToast('请输入验证码');
        return;
    }

    await codeLogin({
        _appName: 'jiakaoshouke',
        _platform: 'web',
        _authVersion: 1.5,
        smsCode: smsCode.value,
        smsId: smsId.value,
        phoneNumber: phone.value,
    })
        .then((res) => {
            userInfoStore.setUserInfo(res);
            smsCode.value = '';
            goHome();
        })
        .catch((error) => {
            errorMessage.value = error.statusText;
        });
};
</script>

<template>
    <div class="lpage">
        <div class="poster"></div>
        <div class="login">
            <h3 class="title">您好，请登录</h3>
            <div class="form">
                <div class="phone">
                    <input @input="phoneInput" type="password" v-model="phone" maxlength="11" placeholder="请输入手机号" />
                </div>
                <div :class="['code', showCode ? '' : 'hide']">
                    <input type="text" @input="smsCodeInput" v-model="smsCode" maxlength="6" placeholder="请输入验证码" />
                    <span class="btn" @click="getCaptcha">{{ smsTimeFlag ? smsTime + '秒' : '获取验证码' }}</span>
                </div>
                <div class="err">{{ errorMessage }}</div>
                <div :class="['submit', canSubmit ? '' : 'disabled']" @click="loginSms">登录</div>
            </div>
        </div>
        <div :class="['mask', showMask ? '' : 'hide']">
            <div id="captcha"></div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.lpage {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.poster {
    width: 652px;
    height: 530px;
    background: url(./images/1.png) no-repeat;
    background-size: 100% 100%;
    margin-right: 90px;
}
.login {
    width: 400px;
    height: 380px;
    border-radius: 16px;
    background-color: #fff;
    padding: 50px 40px 60px 40px;
    display: flex;
    flex-direction: column;

    .title {
        font-size: 28px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.85);
        line-height: 40px;
        text-align: left;
    }

    .form {
        flex: 1;
        padding-top: 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .phone {
        input {
            width: 100%;
            height: 45px;
            background: #ffffff;
            border: 0.7px solid rgba(94, 110, 137, 0.52);
            border-radius: 4px;
            outline: none;
            padding-left: 20px;
            font-size: 16px;
            text-align: left;
            color: rgba(0, 0, 0, 0.45);
            line-height: 22px;
        }
    }

    .code {
        height: 44px;
        display: flex;
        input {
            flex: 1;
            height: 44px;
            background: #ffffff;
            border: 0.7px solid rgba(94, 110, 137, 0.52);
            border-radius: 4px;
            outline: none;
            padding-left: 20px;
            font-size: 16px;
            text-align: left;
            color: rgba(0, 0, 0, 0.45);
            line-height: 22px;
        }
        .btn {
            width: 110px;
            height: 44px;
            background: #04a5ff;
            border-radius: 4px;
            font-size: 16px;
            text-align: center;
            color: #ffffff;
            line-height: 44px;
            margin-left: 12px;
        }
    }
    .err {
        text-align: left;
        color: red;
    }

    .submit {
        background-color: #04a5ff;
        font-size: 16px;
        color: #ffffff;
        line-height: 22px;
        height: 45px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        &.disabled {
            filter: opacity(0.5);
            cursor: default;
        }
    }
}

#captcha {
    width: 480px;
    height: 200px;
}

.hide {
    display: none !important;
}
</style>
