<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();
const goHome = () => {
    router.push({
        name: 'home'
    })
}

</script>

<template>
    <div class="header">
        <div class="logo" @click="goHome"></div>
    </div>
</template>

<style lang="less" scoped>
    .header{
        width: 1180px;
        height: 56px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        .logo{
            cursor: pointer;
            width: 137px;
            height: 26px;
            background: url(./images/<EMAIL>) no-repeat center center/cover;
        }
    }
</style>
