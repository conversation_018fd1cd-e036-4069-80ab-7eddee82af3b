<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import {debounce, isEqual} from 'lodash-es'
import { Carousel } from 'ant-design-vue';
import { WrongListType, CoursewareType } from '@/common/const';
import { KnowledgeItem, TeachPlanItem, getTeachPlan } from '@/api/teach';
import CoursewareSelect from '@/components/CoursewareSelect/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import { useRoute } from 'vue-router';
import userPlanState from '@/store/modules/planState';

type PlanParams = {
    type: CoursewareType,
    id: string
}

const props = defineProps({
    widthMode: {
        type: String,
        default: 'full',
    },
});

const planState = userPlanState()
const { teachPlanData } = storeToRefs(planState);
const carouselRef = ref<typeof Carousel>();
let imgList = ref<TeachPlanItem[]>([]);
let knowledgeList = ref<KnowledgeItem[]>([]);
let teachName = ref<String>('');
let route = useRoute();
let showCoursewareSelect = ref(false);
let loading = false

const { wrongListType, coursewareType, id, specialId, categoryId } = route.query;
const { courseId } = route.params;

const currentKnowledge = computed(() => {
    let knowledge: number = 0;

    imgList.value.forEach((item, index) => {
        if (item.knowledgeId && index <= teachPlanData.value.currentIndex) {
            knowledge = item.knowledgeId;
        }
    });

    return knowledge;
});

let currentRequest = null
const getData = async (params: {type: CoursewareType, id: string}) => {

    loading = true

    if (currentRequest) {
        currentRequest?.abort && currentRequest?.abort()
    }

    currentRequest = getTeachPlan({
        ...params,
        courseId: courseId
    })
    currentRequest.then((data) => {
        teachName.value = data.name;
        imgList.value = data.coursewareResourceList;
        knowledgeList.value = data.knowledgeList;

        onGoTo(teachPlanData.value.currentIndex)
        loading = false
    }).catch(() => {
        loading = false
    });
};

const onChange = async (_from: number, to: number) => {
    teachPlanData.value.currentIndex = to;
};

const onPrev = () => {
    carouselRef.value?.prev();
};

const onNext = () => {
    carouselRef.value?.next();
};

const onGoTo = debounce((index: number) => {
    carouselRef.value?.goTo(index, true);
}, 100)

// 传入多个就一个个的找
const onChangeKnowledge = async (knowledgeId: number | number[], isInside: boolean) => {
    let knowledgeIdList = [];
    const params = getInitParams()
    if (typeof knowledgeId === 'number') {
        knowledgeIdList = [knowledgeId];
    } else {
        knowledgeIdList = knowledgeId;
    }
    if (!isEqual(params, teachPlanData.value.currentParams) && !isInside) {
        teachPlanData.value.currentParams = {}
        await nextTick()
    }
    if (loading) {
        await new Promise((resolve) => {
            let t = setInterval(() => {
                if(!loading) {
                    clearInterval(t)
                    resolve(true)
                }
            }, 16)
        })
    }
    let targetIndex = 0
    outerLoop: for (let j = 0; j < knowledgeIdList.length; j++) {
        if (imgList.value.length) {
            const eleKnowledgeId = knowledgeIdList[j];
            for (let index = 0; index < imgList.value.length; index++) {
                let item = imgList.value[index];
                if (item.knowledgeId === eleKnowledgeId) {
                    targetIndex = index
                    break outerLoop;
                }
            }
        }
    }
    onGoTo(targetIndex)
};

function getInitParams() {
    if (+wrongListType! === WrongListType.Knowledge || +wrongListType! === WrongListType.SpecialPractice || +wrongListType! === WrongListType.AllQuestion) {
        return {
            type: CoursewareType.Special,
            id: specialId as string,
        }
    } else if (+wrongListType! === WrongListType.WrongQuestions || +wrongListType! === WrongListType.LastExamWrongQuestions) {
        return {
            type: CoursewareType.Special,
            id: categoryId as string,
        }
    } else {
        return {
            type: +coursewareType!,
            id: id as string,
        }
    }
}

function initPlanData() {
    const params = getInitParams()
    getData(params);
}

watch(() => teachPlanData.value.currentParams, () => {
    const params = teachPlanData.value.currentParams

    if (!isEqual(params, {})) {
        getData(params)
    } else {
        initPlanData()
    }
}, {
    deep: true,
    immediate: true
});

watch(() => teachPlanData.value.currentIndex, () => {
    onGoTo(teachPlanData.value.currentIndex)
});


function onSelectCourseware(params: {type: CoursewareType, id: string}) {
    teachPlanData.value.currentParams = params
    teachPlanData.value.currentIndex = 0
}

defineExpose({
    onChangeKnowledge,
    onSelectCourseware,
});
</script>

<template>
    <div class="teach-plan-c-box">
        <div class="title">
            <span>
                {{ teachName }}
            </span>
            <a-button style="padding: 0; font-size: 12px; margin-left: 6px;" type="link" @click="showCoursewareSelect = true">切换其他课件 ></a-button>
        </div>
        <div style="height: calc(100% - 40px); display: flex">
            <div style="width: 104px; position: relative; margin-right: 10px; flex-shrink: 0;" v-if="props.widthMode === 'full'">
                <div class="thumb-point-box vertical">
                    <div :class="{ 'active': index === teachPlanData.currentIndex }" class="point-item" v-scrollToView="index === teachPlanData.currentIndex" v-for="(item, index) in imgList" @click="onGoTo(index)">
                        <img :src="item.resourceThumbUrl" />
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="carousel-box">
                    <a-carousel ref="carouselRef" :before-change="onChange" :dots="false" effect="fade" :infinite="false" :key="teachName">
                        <div class="slide-item" v-for="(item, index) in imgList">
                            <img :src="item.resourceUrl" v-if="index === teachPlanData.currentIndex" @click="teachPlanData.showImagePreview = true" />
                        </div>
                    </a-carousel>
                </div>
                <div class="paging-box">
                    <div :class="{ 'no-event': teachPlanData.currentIndex === 0 }" class="prev-icon active-icon" @click="onPrev"></div>
                    <div class="num">{{ teachPlanData.currentIndex + 1 }}/{{ imgList.length }}</div>
                    <div :class="{ 'no-event': teachPlanData.currentIndex === imgList.length - 1 }" class="next-icon active-icon" @click="onNext"></div>
                </div>
                <div class="thumb-point-box horizontal" v-wheelScrollX v-if="props.widthMode !== 'full'">
                    <div :class="{ 'active': index === teachPlanData.currentIndex }" class="point-item" v-scrollToView="index === teachPlanData.currentIndex" v-for="(item, index) in imgList" @click="onGoTo(index)">
                        <img :src="item.resourceThumbUrl" />
                    </div>
                </div>
                <div class="remark-box">
                    <template v-for="(item, index) in imgList">
                        <div class="remark-item" v-if="item.remark" @click="onGoTo(index)">
                            {{ item.remark }}
                        </div>
                    </template>
                </div>
                <div class="knowledge-point-box" v-wheelScrollX>
                    <div :class="{ 'active': item.id === currentKnowledge }" class="point-item" v-scrollToView="item.id === currentKnowledge" v-for="item in knowledgeList" @click="onChangeKnowledge(item.id, true)">{{ item.name }}</div>
                </div>
            </div>
        </div>
        <CoursewareSelect v-model:open="showCoursewareSelect" :courseId="courseId" @onSelect="onSelectCourseware" />
        <ImagePreview  v-model:open="teachPlanData.showImagePreview">
            <template v-for="(item, index) in imgList">
                <img :src="item.resourceUrl" v-if="index === teachPlanData.currentIndex" />
            </template>
        </ImagePreview>
    </div>
</template>

<style lang="less" scoped>
.teach-plan-c-box {
    height: 100%;
    padding: 20px;
    background-color: #fff;
    .title {
        flex-shrink: 0;
        text-align: left;
        position: relative;
        margin-bottom: 8px;
        &::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #04a5ff;
            border-radius: 0px 2px 2px 0px;
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
        }
        span {
            color: #000;
            font-size: 16px;
            font-weight: bold;
        }
    }

    .thumb-point-box.horizontal {
        margin-top: 10px;
        width: 100%;
        padding-bottom: 5px;
        box-sizing: border-box;
        overflow-x: auto;
        display: flex;
        .point-item {
            margin-right: 4px;
        }
    }
    .thumb-point-box.vertical {
        box-sizing: border-box;
        overflow-y: auto;
        position: absolute;
        width: 100%;
        height: 100%;
        .point-item {
            margin-bottom: 6px;
        }
    }
    .thumb-point-box {
        position: relative;
        .point-item {
            cursor: pointer;
            flex-shrink: 0;
            width: 96px;
            height: 54px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid transparent;
            &.active {
                border: 1px solid #04a5ff;
            }
            img {
                max-width: 100%;
                max-height: 100%;
            }
        }
    }
    .content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        width: 100%;
        .carousel-box {
            margin: auto auto 0;
            width: 80%;
            position: relative;
            max-width: 780px;

            :deep(.slick-list) {
                border-radius: 18px;
                background: linear-gradient(180deg, #f0f4f7, #f0f1f6);

                .slick-slide {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;

                    &::before {
                        content: '';
                        display: block;
                        padding-top: 56.25%; /* 9 / 16 = 0.5625，转换为百分比 */
                    }

                    .slide-item {
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        overflow: hidden;
                        display: flex !important;
                        justify-content: center;
                        align-items: center;
                        img {
                            cursor: pointer;
                            max-width: 100%;
                            max-height: 100%;
                        }
                    }
                }
            }
        }

        .paging-box {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px 20px;

            .active-icon {
                width: 26px;
                height: 26px;
                cursor: pointer;

                &.no-event {
                    opacity: 0.5;
                }
            }
            .prev-icon {
                background: url(./images/<EMAIL>) no-repeat center center/cover;
            }
            .num {
                font-size: 16px;
                color: #464646;
                margin: 0 20px;
            }
            .next-icon {
                background: url(./images/<EMAIL>) no-repeat center center/cover;
            }
        }
        .remark-box {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
            .remark-item {
                margin-left: 6px;
                cursor: pointer;
                color: #04a5ff;
            }
        }
        .knowledge-point-box {
            margin-top: auto;
            width: 100%;
            padding-bottom: 5px;
            box-sizing: border-box;
            overflow-x: auto;
            position: relative;
            display: flex;
            .point-item {
                cursor: pointer;
                flex-shrink: 0;
                padding: 20px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                border-radius: 20px;
                margin-right: 10px;
                color: #6e6e6e;
                border: 2px solid #6e6e6e;
                &.active {
                    color: #04a5ff;
                    border: 2px solid #04a5ff;
                }
            }
        }
    }
}
</style>
