<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import userPlanState from '@/store/modules/planState';

const route = useRoute();
const { name } = route.query;
const planState = userPlanState()
const {setQuestionCureentIndex} = userPlanState()
const { questionData } = storeToRefs(planState)
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    questionList: {
        type: Array<any>,
        default: [],
    },
});

const onChangeQuestion = (index: number) => {
    setQuestionCureentIndex(index)
};
</script>

<template>
    <div class="question-list-box">
        <div class="wrong-title">
            {{ props.title }} <span class="knowledge" v-if="name">{{ name }}</span>
        </div>
        <div class="question-list">
            <div @click="onChangeQuestion(index)" v-for="(item, index) in props.questionList" :class="{ active: questionData.currentIndex === index }" class="question-item" v-scrollToView="questionData.currentIndex === index">
                <div class="question-item-title">{{ index + 1 }}. {{ item.title }}</div>
                <div class="question-wrong-count" v-if="item.wrongCount">答错次数：{{ item.wrongCount }}</div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.question-list-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    text-align: left;
    .wrong-title {
        padding-left: 20px;
        flex-shrink: 0;
        color: #000;
        font-size: 16px;
        font-weight: bold;
        text-align: left;
        position: relative;
        margin-bottom: 8px;
        &::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #04a5ff;
            border-radius: 0px 2px 2px 0px;
            position: absolute;
            left: 0px;
            top: 50%;
            transform: translateY(-50%);
        }
        .knowledge {
            margin-left: 6px;
            font-size: 13px;
            font-weight: 400;
            color: #000;
            opacity: 0.65;
        }
    }
    .question-list {
        flex: 1;
        overflow-y: auto;
        padding: 0 20px;
        position: relative;
        .question-item {
            border-bottom: 2px dashed #999;
            padding: 14px 0;
            cursor: pointer;
            &.active {
                .question-item-title {
                    color: #04a5ff;
                }
            }
            .question-item-title {
                font-size: 15px;
                font-weight: bold;
                color: #000;
                opacity: 0.85;
                line-height: 24px;
            }
            .question-wrong-count {
                display: inline-block;
                margin-top: 4px;
                background-color: rgba(255, 74, 64, 0.08);
                padding: 3px 6px;
                font-size: 13px;
                border-radius: 4px;
                color: #fb2e3c;
            }
        }
    }
}
</style>
