<script setup lang="ts">
import { getKnowledgeMap, getTopicDetail } from '@/api/question';
import { QuestionMap } from '@/common/question';
import { computed, nextTick, ref, watch, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import {debounce} from 'lodash-es'
import { message } from 'ant-design-vue';
import CoursewareSelect from '@/components/CoursewareSelect/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import { useRoute } from 'vue-router';
import { WrongListType, CoursewareType } from "@/common/const";
import userPlanState from '@/store/modules/planState';

type QuestionAnswer = {
    qId: QuestionMap['qId'],
    isCorrent: QuestionMap['isCorrent'],
    userSelect: QuestionMap['userSelect']
}

const route = useRoute();
const planState = userPlanState()
const {setQuestionCureentIndex} = userPlanState()
const { questionData } = storeToRefs(planState);
const props = defineProps({
    questionList: {
        type: Array<{
            questionId: number;
            title: string;
            wrongCount: number;
        }>,
        default: [],
    },
    answers: {
        type: Array<number>,
        default: [],
    }
});

const emit = defineEmits<{
    (e: 'onGoKnowledge', value: any): void;
    (e: 'onSelectCourseware', value: any): void;
}>();

const { wrongListType, id } = route.query;
const { courseId } = route.params;
let showCoursewareSelect = ref(false);
let userScrolling = ref(false);

const questionItem = ref<QuestionMap>({} as QuestionMap);

let QuestionContentRef = ref<HTMLElement>()

const showSelectKey = computed(() => {
    return Object.keys(questionData.value.currentAnswer.userSelect || {})
        .sort()
        .join(',');
});

let currentRequest = null
const getQuestionDetail = () => {
    if (currentRequest) {
        currentRequest?.abort && currentRequest?.abort()
    }
    currentRequest = getTopicDetail({
        qIdList: [props.questionList[questionData.value.currentIndex].questionId],
    });
    // todo Callback Hell
    currentRequest.then((data) => {
        currentRequest = getKnowledgeMap({ questionList: data })
        currentRequest.then(async list => {
            questionItem.value = list[0]
            
            if (props.answers[questionData.value.currentIndex]) {
                const indexMap: Record<number, number> = { 16: 0, 32: 1, 64: 2, 128: 3 };
                const answer = props.answers[questionData.value.currentIndex]
                const answerList = Object.keys(indexMap).map((item, index) => {
                    return !!((16 << index) & answer) ? +item : 0
                }).filter(item => item)
                answerList.forEach(item => {
                    const aIndex = indexMap[item]
                    const userSelect = questionItem.value.options[aIndex]

                    onSelectAnswer(userSelect.showKey, userSelect.value, userSelect.correct ? 1 : 0)
                })
                onMultSelectAnswer()
            }

            await nextTick()
            setScrollTop(questionData.value.contentScrollTop)
        })
    })
};

watchEffect(() => {
    if (props.questionList[questionData.value.currentIndex]) {
        getQuestionDetail();
    }
});

const onSelectAnswer = (showKey: string, value: string, correct: number) => {
    const userSelect = {
        showKey,
        value,
        correct,
    };
    let selectRight = false;

    // 作答过的题目或者展示答案的时候不能作答
    if (questionData.value.currentAnswer.isCorrent !== 1) {
        return false;
    }

    // 单选题现在就计算答案(多选就return);
    if (questionItem.value.optionType <= 1) {
        selectRight = userSelect.correct === 1;
        // 将选择的结果赋值给当前数组对应的数据
        questionData.value.currentAnswer.userSelect[userSelect.showKey] = userSelect;
        questionData.value.currentAnswer.isCorrent = selectRight ? 2 : 3;
    } else {
        if (questionData.value.currentAnswer.userSelect[userSelect.showKey]) {
            // 多选的时候如果多次点击相同一个选项，第二次点击就是取消
            delete questionData.value.currentAnswer.userSelect[userSelect.showKey];
        } else {
            questionData.value.currentAnswer.userSelect[userSelect.showKey] = userSelect;
        }
    }
};

const onMultSelectAnswer = () => {
    let selectRight = false;

    // 作答过的题目不能再次作答
    if (questionData.value.currentAnswer.isCorrent !== 1) {
        return false;
    }
    // 如果是只选了一个需要提醒
    if (Object.keys(questionData.value.currentAnswer.userSelect).length < 2) {
        message.info('请选择2个及以上答案');
        return false;
    }
    // 判断之前要先sort，因为b,a != a,b
    selectRight = questionItem.value.StrCorrect === Object.keys(questionData.value.currentAnswer.userSelect).sort().join(',');

    questionData.value.currentAnswer.isCorrent = selectRight ? 2 : 3;
};

const onNext = () => {
    const index = questionData.value.currentIndex + 1;
    setQuestionCureentIndex(index)
};
const onPrev = () => {
    const index = questionData.value.currentIndex - 1;
    setQuestionCureentIndex(index)
};

const contentScroll = debounce((e) => {
    if (userScrolling.value) {
        const scrollTop = e.srcElement.scrollTop
        questionData.value.contentScrollTop = scrollTop
    }
}, 16)

const onGoKnowledge = (knowledge: number | number[] = 0) => {
    if ((+wrongListType! === WrongListType.Knowledge && id) || +wrongListType! === WrongListType.WrongQuestions || +wrongListType! === WrongListType.LastExamWrongQuestions || +wrongListType! === WrongListType.SpecialPractice || +wrongListType! === WrongListType.AllQuestion) {
        emit('onGoKnowledge', knowledge);
    } else {
        message.info('没有知识点');
    }
};

const setScrollTop = async (scrollTop: any) => {
    const content = QuestionContentRef?.value as HTMLElement
    if (!content) return
    if (content.scrollTo) {
        content.scrollTo({
            top: scrollTop,
            behavior: "smooth",
        });
    } else {
        content.scrollTop = scrollTop
    }
};

watch(() => questionData.value.contentScrollTop, (val) => {
    setScrollTop(val)
})

function onSelectCourseware(params: {type: CoursewareType, id: string}) {
    emit('onSelectCourseware', params);
}
</script>

<template>
    <div class="practice" v-if="questionItem.qId" :key="questionItem.qId">
        <div class="swiper-slide" :data-qId="questionItem.qId" @scroll="contentScroll" v-onUserScroll="(val) => userScrolling = val" ref="QuestionContentRef">
            <div class="top">
                <span class="topic-type">{{ questionItem.showOptionType }}</span>
                <span class="topic-title">{{ questionItem.title }}</span>
            </div>

            <template v-if="questionItem.StrCorrect">
                <div class="show" v-if="questionItem.mediaType !== 0">
                    <img v-if="questionItem.mediaType === 1" :src="questionItem.mediaContent" @click="questionData.showImagePreview = true" width="180" />
                    <video v-else id="video" :src="questionItem.mediaContent" autoplay controls></video>
                </div>

                <div class="select-answer">
                    <div class="answer-list">
                        <template v-if="questionItem.optionType <= 1" v-for="item in questionItem.options">
                            <!-- 只要和用户选择一样的先给showWrong，如果答案是对的，再给showRight -->

                            <div
                                v-if="item.showKey"
                                :class="{
                                    showWrong: questionData.currentAnswer.userSelect[item.showKey],
                                    showRight: item.correct && questionData.currentAnswer.isCorrent != 1,
                                }"
                                class="answer-item"
                                @click="onSelectAnswer(item.showKey, item.value, item.correct ? 1 : 0)"
                            >
                                <span class="item-icon image-box">{{ item.showKey }}</span>
                                <span class="text">{{ item.value }}</span>
                            </div>
                        </template>
                        <template v-else>
                            <template v-for="item in questionItem.options">
                                <!-- 只要选中并且不是背题模式并且没有做题就给类名select  只要答案是正确的并且做题了并且选择答案的选项没有就给类名rightSelect，如果题目做了并且选的当前选项是错误的并且不是背题模式就给类名showWrong，答案是正确的并且展示答案或者选择的选项中有这个 -->
                                <!-- 只要是背题模式就肯定是showRight -->
                                <div
                                    v-if="item.showKey"
                                    :class="{
                                        select: questionData.currentAnswer.userSelect[item.showKey] && questionData.currentAnswer.isCorrent == 1,
                                        rightSelect: item.correct && questionData.currentAnswer.isCorrent != 1 && !questionData.currentAnswer.userSelect[item.showKey],
                                        showWrong: questionData.currentAnswer.userSelect[item.showKey] && questionData.currentAnswer.isCorrent != 1 && !item.correct,
                                        showRight: item.correct && questionData.currentAnswer.userSelect[item.showKey] && questionData.currentAnswer.isCorrent != 1,
                                    }"
                                    class="answer-item"
                                    @click="onSelectAnswer(item.showKey, item.value, item.correct ? 1 : 0)"
                                >
                                    <span class="item-icon">{{ item.showKey }}</span>
                                    <span class="text">{{ item.value }}</span>
                                </div>
                            </template>
                            <div v-if="questionData.currentAnswer.isCorrent === 1" @click="onMultSelectAnswer" class="submit-answer">确认答案</div>
                        </template>
                    </div>
                </div>

                <div :class="{ hide: questionData.currentAnswer.isCorrent === 1 }" class="answer-box">
                    <div class="answer">
                        答案：
                        <span class="rightStr">{{ questionItem.StrCorrect }}</span>
                        &nbsp;&nbsp;您选择：<span class="selectStr {{questionItem.StrCorrect === showSelectKey?S.right:S.wrong}}">{{ showSelectKey }}</span>
                    </div>
                    <div class="detail">
                        <div class="detail-header">试题详解</div>
                        <div class="detail-title">题目解析</div>
                        <div class="detail-info" v-html="questionItem.showExplain"></div>
                    </div>
                </div>
            </template>
        </div>
        <div class="position">
            <div class="knowledge-box">
                <div class="knowledge-list">
                    <a-button style="padding: 0; font-size: 14px; margin-top: 10px; background-color: #fff;" type="link" @click="showCoursewareSelect = true">切换其他课件 ></a-button>
                    <div v-for="item in questionItem.knowledgeList" :key="item.id" class="knowledge-item" @click="onGoKnowledge(item.id)">
                        <div>#{{ item.name }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-box">
            <div class="prev-next-box">
                <div @click="onPrev" :class="{ 'no-event': questionData.currentIndex === 0 }" class="prev btn">上一题</div>
                <div @click="onNext" :class="{'no-event': questionData.currentIndex as number >= questionList.length - 1}" class="next btn">下一题</div>
            </div>
        </div>
        
        <CoursewareSelect v-model:open="showCoursewareSelect" :courseId="courseId" @onSelect="onSelectCourseware" />
        <ImagePreview  v-model:open="questionData.showImagePreview">
            <img :src="questionItem.mediaContent" />
        </ImagePreview>
    </div>
</template>

<style lang="less" scoped>
.practice {
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    .swiper-slide {
        flex: 1;
        overflow: auto;
        text-align: left;
        padding: 20px 20px 100px;
        scroll-behavior: smooth;
        .image-box {
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
        }

        img {
            cursor: pointer;
        }

        .top {
            .topic-type {
                display: inline-block;
                height: 18px;
                line-height: 18px;
                text-align: center;
                background-color: #1dacf9;
                color: white;
                font-size: 12px;
                border-radius: 5px 5px 5px 0;
                padding: 0 8px;
                vertical-align: top;
                margin-right: 2px;
                margin-top: 4px;
                transform: scale(0.9);
            }

            .topic-title {
                font-size: 26px;
                line-height: 32px;
                color: #333 !important;
            }
        }

        .show {
            margin-top: 15px;
            padding: 0 15px;
            box-sizing: border-box;
            text-align: center;

            image,
            video {
                max-width: 100%;
            }
        }

        .select-answer {
            margin-top: 13px;

            .answer-list {
                .answer-item {
                    cursor: pointer;
                    box-sizing: border-box;
                    min-height: 52px;
                    padding: 7px 15px;
                    display: flex;
                    align-items: center;
                    color: #333;

                    .item-icon {
                        width: 28px;
                        height: 28px;
                        box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.15);
                        border: 1px solid rgba(0, 0, 0, 0.05);
                        border-radius: 50%;
                        font-size: 16px;
                        text-align: center;
                        line-height: 28px;
                        margin-right: 15px;
                        flex-shrink: 0;
                    }

                    .text {
                        font-size: 26px;
                        line-height: 32px;
                    }

                    /* 多选选中的选项 */
                    &.select {
                        .item-icon {
                            border: none;
                            background-color: #b0b7c6;
                            color: white;
                        }
                    }

                    /* 正确的选项 */
                    &.rightSelect:not(.select) {
                        color: #1dacf9;

                        .item-icon {
                            background-color: #1dacf9;
                            color: white;
                        }
                    }

                    /* 答案选择错误 */
                    &.showWrong {
                        .item-icon {
                            position: relative;

                            &:after {
                                content: '';
                                position: absolute;
                                width: 38px;
                                height: 38px;
                                background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-wrong.png) no-repeat center center/cover;
                                top: -5px;
                                left: -5px;
                            }
                        }

                        .text {
                            color: #ff4a40;
                        }
                    }

                    /* 答案选择正确 */
                    &.showRight {
                        .item-icon {
                            position: relative;

                            &:after {
                                content: '';
                                position: absolute;
                                width: 38px;
                                height: 38px;
                                background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/jkbd-ic-dati-right.png) no-repeat center center/cover;
                                top: -5px;
                                left: -5px;
                            }
                        }

                        .text {
                            color: #1dacf9 !important;
                        }
                    }
                }

                .submit-answer {
                    height: 44px;
                    margin: 72px auto 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(90deg, rgba(0, 224, 229, 1) 0%, rgba(0, 134, 250, 1) 100%);
                    border-radius: 22px;
                    color: white;
                    font-size: 17px;
                }
            }
        }

        .answer-box {
            &.hide {
                display: none;
            }
        }

        .answer {
            height: 44px;
            display: flex;
            align-items: center;
            margin: 13px auto 15px;
            background-color: #f3f6f8;
            color: #333;
            font-size: 17px;
            font-weight: bold;
            border-radius: 2px;
            padding-left: 15px;
            box-sizing: border-box;

            .rightStr {
                color: #1dacf9;
            }

            .selectStr {
                &.right {
                    color: #1dacf9;
                }

                &.wrong {
                    color: #ff4a40;
                }
            }
        }

        .skills-box {
            padding: 25px 15px 20px;

            .skills-title {
                font-size: 16px;
                color: #333;
                height: 22px;
                display: flex;
                align-items: center;
                font-weight: bold;

                &:before {
                    content: '';
                    width: 4px;
                    height: 14px;
                    border-radius: 2px;
                    background-color: #1dacf9;
                    margin-right: 8px;
                }
            }

            .skills {
                margin: 15px 0;
                font-size: 17px;
                line-height: 26px;
                color: #333;

                .skills-mask {
                    display: none;
                }
            }

            .skills-detail {
                display: block;
                background-color: #f3f6f8;
                padding: 10px;
                font-size: 14px;
                line-height: 22px;
                color: #464646;
                box-sizing: border-box;
            }

            &.no-vip {
                .skills {
                    white-space: nowrap;
                    overflow: hidden;
                    position: relative;

                    .skills-mask {
                        position: absolute;
                        top: 0;
                        right: 0;
                        display: inline-block;
                        height: 100%;
                        display: flex;

                        .mask-icon {
                            width: 70px;
                            height: 100%;
                            background: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/sketch6ae6ccdd-f084-40fb-be13-7c70920c6e8e.png) no-repeat center center/cover;
                        }

                        .look-all-box {
                            padding-left: 10px;
                            background-color: #fff;

                            .look-all {
                                width: 118px;
                                height: 24px;
                                background: linear-gradient(90deg, rgba(249, 246, 229, 1) 0%, rgba(232, 224, 185, 1) 100%);
                                border-radius: 12px;
                                font-size: 13px;
                                color: #625328;
                                font-weight: bold;
                                line-height: 24px;
                                position: relative;
                                padding-left: 14px;
                                box-sizing: border-box;

                                .go-icon {
                                    position: absolute;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    right: 10px;
                                    width: 7px;
                                    height: 12px;
                                    background-image: url(https://web-resource.mucang.cn/minprogram/jiakaobaodian/sketch66bc730d-8008-40e8-a336-776e9d85786d.png);
                                }
                            }
                        }
                    }
                }

                .skills-detail {
                    display: none;
                }
            }
        }

        .divider {
            height: 8px;
        }

        .detail {
            padding: 15px 15px 0px;

            .star-title {
                font-size: 17px;
                margin-bottom: 12px;
            }

            .detail-header {
                margin-top: 20px;
                text-align: center;
                font-size: 20px;
                font-weight: bold;
                color: #000000;
                line-height: 28px;
            }

            .detail-title {
                margin-top: 15px;
                font-size: 16px;
                color: #333;
                height: 22px;
                display: flex;
                align-items: center;
                font-weight: bold;
                margin-bottom: 15px;

                &:before {
                    content: '';
                    width: 4px;
                    height: 14px;
                    border-radius: 2px;
                    background-color: #1dacf9;
                    margin-right: 8px;
                }
            }

            .detail-info {
                margin-top: 15px;

                font-size: 24px;
                line-height: 32px;
                color: #333;
            }
        }
    }

    .position {
        position: absolute;
        bottom: 90px;
        right: 0;
        width: 100%;
        padding: 0 20px;
        .knowledge-box {
            .knowledge-list {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                flex-wrap: wrap;
                .knowledge-item {
                    margin-top: 10px;
                    margin-left: 10px;
                    background-color: #fff;
                    overflow: hidden;
                    > div {
                        cursor: pointer;
                        background-color: rgba(248, 178, 77, 0.1);
                        color: #ff822d;
                        padding: 3px 7px;
                        border-radius: 4px;
                        overflow: hidden;
                        text-wrap: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
    }

    .footer-box {
        padding: 0 20px;
        margin-top: 20px;
        flex-shrink: 0;
        margin-bottom: 20px;
        .prev-next-box {
            border: 1px solid #04a5ff;
            height: 48px;
            border-radius: 8px;
            display: flex;
            position: relative;
            cursor: pointer;
            &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 1px;
                height: 24px;
                background-color: #04a5ff;
            }
            .btn {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #04a5ff;
                font-size: 16px;
                &.no-event {
                    pointer-events: none;
                    color: rgba(4, 165, 255, 0.4);
                }
            }
        }
    }
}
</style>
