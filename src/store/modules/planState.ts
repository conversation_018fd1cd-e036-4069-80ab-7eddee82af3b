import { ref } from "vue";
import { defineStore } from 'pinia';

const usePlanState = defineStore('planState', () => {
  const questionData = ref({
    currentIndex: 0,
    isOpenTeach: false,
    contentScrollTop: -1,
    currentAnswer: {
      isCorrent: 1,
      userSelect: {}
    },
    showImagePreview: false
  })
  const teachPlanData = ref({
    currentIndex: 0,
    currentParams: {},
    showImagePreview: false
  })
  const examTotalData = ref({
    page: 1,
    efficientExam: false,
    examListScrollTop: -1,
  })

  function setQuestionCureentIndex(index: number) {
    questionData.value.currentIndex = index;
    questionData.value.contentScrollTop = 0;
    questionData.value.currentAnswer = {
      isCorrent: 1,
      userSelect: {}
    }
  }

  return {
    questionData, teachPlanData, examTotalData, setQuestionCureentIndex
  }
});

export default usePlanState;
