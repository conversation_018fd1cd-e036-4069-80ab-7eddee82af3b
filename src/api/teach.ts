import { CoursewareType } from "@/common/const";
import { request } from "@/common/request";
import { getTopicDetail } from "./question";
import { dateFormat } from '@/common/utils';

export enum TeachType {
    Wrong = 1,
    Special = 2,
    Sprint = 3,
    Exam = 4,
    Knowledge = 5
}

export interface HomeItem {
    type: TeachType
    contentId: number
    contentList?: {
        id: number,
        name: string
    }[]
    detailId: number,
    displaySpecial: boolean,
    scope: number
}

export enum HomeStatus {
    Success = 1,
    Working = 2,
    NoContent = 3
}

export function getListPlanContent(params: { courseId: number }): Promise<{
    status: HomeStatus
    readyTime: string
    lecturerNickName: string
    planName: string
    itemList: HomeItem[]
}> {
    return request({
        hostName: 'parrot',
        url: '/api/web/pts-lesson-plan/list-plan-content.htm',
        method: 'POST',
        data: {
            courseId: params.courseId
        }
    });
};

export interface WrongSummaryItem {
    specialId: number
    specialName: string
    itemList: {
        knowledgeId: number,
        knowledgeName: string
        totalCount: number
        doneCount: number
        wrongCount: number
    }[]
}

export function getWrongSummary(params: { contentId: number }): Promise<{
    itemList: WrongSummaryItem[]
}> {

    return request({
        hostName: 'parrot',
        url: '/api/web/pts-lesson-plan/get-wrong-summary.htm',
        data: {
            contentId: params.contentId
        }
    });
};

export interface TeachPlanItem {
    resourceType: number
    resourceUrl: string
    resourceThumbUrl: string
    knowledgeId: number
    knowledgeName: string
    remark: string
}

export interface KnowledgeItem {
    name: string,
    id: number
}

export function getTeachPlan(params: { type: CoursewareType, id: string, courseId?: string }): Promise<{
    id: number,
    name: string,
    type: number
    coursewareResourceList: TeachPlanItem[]
    knowledgeList: KnowledgeItem[]
}> {
    let promise: Promise<any>;
    const serialize = data => {
        let knowledgeList: KnowledgeItem[] = [];

        data.coursewareResourceList.forEach((item: any) => {
            let flag = false;
            knowledgeList.forEach(ele => {
                if (item.knowledgeId === ele.id) {
                    flag = true;
                }
            })
            if (!flag && item.knowledgeId) {
                knowledgeList.push({
                    id: item.knowledgeId,
                    name: item.knowledgeName
                })
            }
        })
        data.coursewareResourceList = data.coursewareResourceList.map((item: any) => {
            let {resourceUrl} = item
            let resourceThumbUrl = resourceUrl
            if (resourceUrl) {
                resourceUrl = resourceUrl + '!large'
                resourceThumbUrl = resourceThumbUrl + '!small'
            }
            return {
                ...item,
                resourceUrl,
                resourceThumbUrl
            }
        })

        return {
            ...data,
            knowledgeList
        }
    }

    if (params.type === CoursewareType.Special) {
        promise = request({
            hostName: 'parrot',
            url: '/api/web/courseware/get-courseware-detail-by-practice-id.htm',
            data: {
                practiceId: params.id,
                courseId: params.courseId
            },
            serialize
        });
    } else if (params.type === CoursewareType.Exam) {
        promise = request({
            hostName: 'parrot',
            url: '/api/web/courseware/get-courseware-detail.htm',
            data: {
                coursewareId: params.id
            },
            serialize
        });
    } else {
        return Promise.reject(new Error('当前类型不在定义之中'))
    }

    return promise
};

export const acceptCourse = function (params: { courseId: number }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/course/accept-course.htm',
        data: {
            courseId: params.courseId
        }
    })
};

export const getStudentSummaryWrongList = function (params: { sno: string, carType: string, kemu: string, sceneCode: string, filed: string, categoryId: number }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/student/get-student-summary.htm',
        data: {
            sno: params.sno,
            carType: params.carType,
            kemu: params.kemu,
            sceneCode: params.sceneCode,
        }
    }).then(async (data) => {
        const {wrongBook} = data
        const filedData = wrongBook[params.filed]
        const item = filedData.find((item: any) => item.categoryId === params.categoryId)
        let questionList = await getTopicDetail({
            qIdList: item.questionList.map((item: any) => item.questionId)
        });

        return {
            itemList: item.questionList.map((item: any, index: number) => ({
                questionId: item.questionId,
                wrongCount: item.wrongCount,
                title: questionList[index].title
            }))
        }
    })
};

export const getMockExamWrongList = function (params: { sno: string, kemu: string, sceneCode: string, carType: string, uniqueId: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/student/list-all-mock-exam-record.htm',
        data: {
            sno: params.sno,
            kemu: params.kemu,
            sceneCode: params.sceneCode,
            carType: params.carType,
            page: 1,
            limit: 9999,
        }
    }).then(async (data) => {
        const item = data.itemList.find((item: any) => item.uniqueId === params.uniqueId)
        let questionList = await getTopicDetail({
            qIdList: item.errorqIdList
        });

        return {
            itemList: item.errorqIdList.map((questionId: any, index: number) => ({
                questionId,
                title: questionList[index].title
            }))
        }
    })
};

export const getCourseExamWrongList = function (params: { examineEncodeId: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/pts-lesson-examine/get-examine-info.htm',
        data: {
            examineEncodeId: params.examineEncodeId,
        }
    }).then(async (data) => {
        let questionList = await getTopicDetail({
            qIdList: data.questionIds
        });

        return {
            itemList: data.questionIds.map((questionId: any, index: number) => ({
                questionId,
                title: questionList[index].title
            })),
            answers: data.answers
        }
    })
};

export function getWrongList(params: { contentId: string, knowledgeId: number | null, specialId: string, needAllQuestions: boolean }): Promise<{
    itemList: {
        questionId: number,
        title: string
        wrongCount: number
    }[]
}> {

    return request({
        hostName: 'parrot',
        url: '/api/web/pts-lesson-plan/get-wrong-detail.htm',
        data: {
            contentId: params.contentId,
            specialId: params.specialId,
            knowledgeId: params.knowledgeId,
            needAllQuestions: params.needAllQuestions
        }
    }).then(async (data) => {
        let questionList = await getTopicDetail({
            qIdList: data.itemList.map((item: any) => item.questionId)
        });

        return {
            itemList: data.itemList.map((item: any, index: number) => ({
                ...item,
                title: questionList[index].title
            }))
        }
    })
};

export const getCourseList = function (params: { sno: string, carType: string, tutorKemu: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/student/list-course-schedule.htm',
        data: params
    })
};

export const getMockExamList = function (params: { sno: string, page: number, limit: number, carType: string, kemu: string, sceneCode: string, effectiveMockExam?: boolean }): Promise<{
    itemList: [],
    total: number
}> {
    return request({
        hostName: 'parrot',
        url: '/api/web/student/list-all-mock-exam-record.htm',
        data: params
    }).then(async (data) => {
        data.itemList?.forEach((item: any) => {
            item.period = Math.floor(item.durationMillis / 1000);
            item.showExamTime = dateFormat(item.examTime, 'yyyy-MM-dd hh:mm:ss');
            item.showPeriod = Math.floor(item.period / 60) + '分' + item.period % 60 + '秒'
        });

        return data;
    })
};

export const getSpecialListStore = function (params: { courseId: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/course/get-special-list.htm',
        data: {
            courseId: params.courseId
        }
    }).then(data => {
        return data.itemList;
    });
};
export const getSpecialQuestionList = function (params: { specialId: string, courseId: number, specialSource: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/course/get-questions.htm',
        data: {
            specialId: params.specialId,
            courseId: params.courseId,
            specialSource: params.specialSource
        }
    }).then(async data => {
        let questionList = await getTopicDetail({
            qIdList: data.itemList.map((item: any) => item)
        });

        return {
            itemList: data.itemList.map((item: any, index: number) => ({
                questionId: item,
                title: questionList[index].title
            }))
        }
    });
};

export const getLecturerCourseListStore = function (params: { page: number, limit: number }) {

    // return new Promise(resolve => {
    //     const data = {
    //         data: [{
    //             "courseId": 1,
    //             "courseNo": "三六节来出实来六细体玖不十细十不机适具三适串适合滴十十三的出三十叁这来是四适叁伍在具陆适陆柒五这四在机十在实机我滴八玖适具四在滴实不叁意九滴不六2",
    //             "status": 1,
    //             "subject": "十不四字肆玖更壹意拾不机4",
    //             "studentName": "三想具",
    //             "studentNickName": "实不五",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "随捌四叁",
    //             "homeworks": [{
    //                 "id": 395,
    //                 "courseScheduleId": 867,
    //                 "subject": "壹二字叁",
    //                 "type": 800,
    //                 "bizValue": "适细",
    //                 "bizName": "八三叁滴",
    //                 "status": 534
    //             }, {
    //                 "id": 714,
    //                 "courseScheduleId": 647,
    //                 "subject": "八适合具",
    //                 "type": 372,
    //                 "bizValue": "这是体肆捌",
    //                 "bizName": "合节细来",
    //                 "status": 171
    //             }]
    //         }, {
    //             "courseId": 2,
    //             "courseNo": "不肆五的实滴意不在体符机贰一想七二拾符是想五要要机细六五滴三串更叁细2",
    //             "status": 2,
    //             "subject": "不要在出来体随玖2",
    //             "studentName": "肆字八八四",
    //             "studentNickName": "陆节不",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "细在滴更",
    //         }, {
    //             "courseId": 3,
    //             "courseNo": "六我十我在串这不不在我要出二五实四体柒想肆肆意细不在三体捌柒玖玖在九意3",
    //             "status": 2,
    //             "subject": "要随实是适的不意捌这要九不九柒更在的玖是这六3",
    //             "studentName": "体滴一要",
    //             "studentNickName": "的一三是我",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "一在伍合",
    //         }, {
    //             "courseId": 4,
    //             "courseNo": "四是串四柒随这在我拾细壹壹具贰伍拾六要四八六七玖节六实七肆伍伍滴一节伍贰我串七合玖的柒三这六串柒三出九字具六不九六节字4",
    //             "status": 2,
    //             "subject": "我串柒贰随具在体串陆要六在符合字在4",
    //             "studentName": "贰拾陆符不",
    //             "studentNickName": "符适五细",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "肆在",

    //         }, {
    //             "courseId": 5,
    //             "courseNo": "要机字串八肆体在柒在捌二出陆节七二随叁体出这柒十在更我在出随捌一九体八陆八九玖体四在节串5",
    //             "status": 2,
    //             "subject": "不玖意十二细随玖我拾一这三符伍更陆实柒不十来八不的具不十壹玖玖拾这想要二在字陆想具捌六肆5",
    //             "studentName": "更伍",
    //             "studentNickName": "柒这",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "拾想",

    //         }, {
    //             "courseId": 6,
    //             "courseNo": "符是细捌壹陆不叁四串六滴实我串五更叁贰字十捌机具细更一四伍适捌节在机六字在更出五想我意的肆十的捌不串的节四柒的符要6",
    //             "status": 2,
    //             "subject": "三十八实节伍叁一要更贰具伍体叁六不来更柒八符四符的我节这更来来来串适肆来具随肆实七细是适具叁适更叁来不贰细九来不串串符柒肆出在机七八在细在在要叁串6",
    //             "studentName": "要壹",
    //             "studentNickName": "四三是来",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "节符陆肆这",

    //         }, {
    //             "courseId": 7,
    //             "courseNo": "节柒四合捌不的不四随更滴八五叁细拾八机叁二符一的随滴更五九壹我九要一滴字伍贰肆五柒柒更四捌十八体这不字叁捌串陆叁字在在九八不意拾细拾在拾四意字三是九想四合九陆细肆六肆不十伍机意捌7",
    //             "status": 2,
    //             "subject": "七更体三想十在实来壹在六八九意出不十十是我要这柒这更柒在节三串更贰随体这实叁九叁不随更在随机来来更适合九八七细八七适在柒八具随伍串伍的在想想七7",
    //             "studentName": "柒在是",
    //             "studentNickName": "伍四",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "体二来这二",

    //         }, {
    //             "courseId": 8,
    //             "courseNo": "要不九串串符更在在在在伍更出四十十这在贰陆合体8",
    //             "status": 2,
    //             "subject": "不体不节不字要不不实这玖三适玖伍一是六字随柒贰想意五意五五符更九十机三肆在八六要随具九贰是拾一合伍四伍实在贰一十滴贰三不适壹叁八肆是在五符壹想陆不叁8",
    //             "studentName": "陆三",
    //             "studentNickName": "叁不二在节",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "是的",

    //         }, {
    //             "courseId": 9,
    //             "courseNo": "机四细要贰是体叁玖字壹三四节拾机二意陆是一想节出体六二细符具六出节伍在随六九七壹想六更来壹这拾不要节适更八八9",
    //             "status": 2,
    //             "subject": "节伍实出陆不肆在字的四二壹随适陆玖是滴玖具十这合伍串体这叁随壹字机七实六陆一合拾壹伍叁九伍的二柒意八七柒体符九肆一来实合字机九更七的陆合串壹八来合适拾体随叁九来节捌贰机八体9",
    //             "studentName": "适在适",
    //             "studentNickName": "九捌",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "体九不捌我",

    //         }, {
    //             "courseId": 10,
    //             "courseNo": "实五二具不捌捌伍细意陆要是壹七伍肆一这符具适要要串一是更一我体陆的拾这这叁字玖一来是想是七九符实玖随意拾字意体七二在四不壹适柒贰肆合实捌10",
    //             "status": 2,
    //             "subject": "不不想细玖六在七随细五适更玖捌叁的陆的不细要捌一这字四随在来捌这是肆字要十的要实具柒体八具肆捌更在柒拾贰玖玖四九符六想这四八意在六伍节10",
    //             "studentName": "肆滴壹串",
    //             "studentNickName": "不五",
    //             "beginTime": new Date().getTime(),
    //             "endTime": new Date().getTime() + 24 * 3600 * 1000,
    //             "supervisorName": "三滴来",

    //         }],
    //         paging: {
    //             "page": params.page,
    //             "total": 100
    //         }
    //     };

    //     resolve(data);
    // });


    return request({
        hostName: 'parrot',
        url: '/api/web/course/get-lecturer-all-course.htm',
        returnStore: true,
        data: params
    });
};

export const getCourseStatisticStore = function () {

    // return new Promise(resolve => {
    //     resolve({
    //         totalCount: 10,
    //         unBeginCount: 11,
    //         finishedCount: 12,
    //         settleCount: 13,
    //         waitSettleCount: 14
    //     });
    // })

    return request({
        hostName: 'parrot',
        url: '/api/web/course/course-statistic.htm',
    });
};

export const setHomeWork = function (params: {
    courseScheduleId: string
    homeworkList: string,
    specialSource: number,
}) {
    return request({
        hostName: 'parrot',
        url: '/api/web/course/update-homework.htm',
        method: 'POST',
        data: params
    });
}

export const submitReviewCourse = function (params: {
    courseId: string,
    comment: string
}) {
    return request({
        hostName: 'parrot',
        url: '/api/web/course/submit-review-course.htm',
        method: 'POST',
        data: params
    });
}


export const getLatestCourseStore = function (params: { courseId: number }) {
    // return new Promise(resolve => {
    //     resolve({
    //         subject: '测试',
    //         beginTime: new Date().getTime() + 24 * 3600 * 1000 * 20 - 2 * 3600 * 1000,
    //         endTime: new Date().getTime() + 24 * 3600 * 1000 * 20,
    //         serverTime: new Date().getTime(),
    //         studentName: '讲师姓名',
    //         status: 1
    //     })
    // })
    return request({
        hostName: 'parrot',
        url: '/api/web/course/get-course.htm',
        data: params
    })
}

export const getCoursewareList = function (params: { courseId: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/courseware/get-course-list-by-type.htm',
        data: params
    });
}

export const getRoomInfo = function (params: { roomNo: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/teaching-room/get-teaching-room-info.htm',
        data: params
    })
}

export const kemuParamsMap = {
    10: {
        kemu: 1,
        sceneCode: 101
    },
    20: {
        kemu: 4,
        sceneCode: 101
    },
    30: {
        kemu: 1,
        sceneCode: 102
    },
    40: {
        kemu: 1,
        sceneCode: 101
    }
}

export const getStudentBaseInfo = function (params: { sno: string }) {
    return request({
        hostName: 'parrot',
        url: '/api/web/student/get-student-base-info.htm',
        data: params
    })
}