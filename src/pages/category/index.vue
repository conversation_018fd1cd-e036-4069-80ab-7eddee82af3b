<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { getWrongSummary, WrongSummaryItem } from '@/api/teach';
import { WrongListType } from '@/common/const';
import { message } from 'ant-design-vue';
import { Swiper, SwiperSlide } from 'swiper/vue';

import 'swiper/css';

import 'swiper/css/free-mode';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { FreeMode, Pagination, Navigation } from 'swiper/modules';
import { MessageTypeEnum,postMessage } from '@/common/message';

const router = useRouter();
const route = useRoute();
const list = ref<WrongSummaryItem[]>([]);
const { contentId } = route.query;

onMounted(() => {
    getData();
});

const getData = () => {
    getWrongSummary({ contentId: +contentId! }).then((data) => {
        list.value = data.itemList;

        if (!list.value?.length) {
            message.error('暂时没有错题');
        }
    });
};

onBeforeRouteLeave((to, from, next) => {
    // 使用 router.resolve 方法来获取完整的URL
    const fullUrl = window.location.origin + location.pathname + location.search + '#' + to.fullPath;

    // 打印即将跳转的完整URL
    console.log('即将跳转的完整URL:', fullUrl);

    postMessage(MessageTypeEnum.OpenPage, {
        data: {
            url: fullUrl,
        },
    });

    // 继续路由导航
    next();
});

const onGoAllQuestion = async (type = WrongListType.AllQuestion, special: WrongSummaryItem) => {
    router.push({
        name: 'wrongList',
        query: {
            wrongListType: type,
            contentId,
            specialId: special.specialId,
            name: special.specialName,
        },
    });
}

const onGoWrongList = async (type = WrongListType.Knowledge, knowledgeInfo: { knowledgeId: number; knowledgeName: string; wrongCount: number }, specialId: number) => {
    if (knowledgeInfo.wrongCount === 0) {
        message.error('这个知识点下没有错题，请学员继续练习！');
        return;
    }

    router.push({
        name: 'wrongList',
        query: {
            wrongListType: type,
            contentId,
            specialId,
            name: knowledgeInfo.knowledgeName,
            id: knowledgeInfo.knowledgeId,
        },
    });
};

const calcWrongRate = (wrongCount: number, doneCount: number) => {
    return doneCount ? ((wrongCount * 100) / doneCount).toFixed(1) : 0;
};
</script>

<template>
    <div class="cpage">
        <swiper
            :slidesPerView="3.5"
            :navigation="true"
            :pagination="{
                clickable: true,
            }"
            :spaceBetween="30"
            :modules="[FreeMode, Pagination, Navigation]"
            :freeMode="true"
        >
            <swiper-slide v-for="item in list">
                <div class="cate">
                    <div class="cline">
                        <div class="ctitle">
                            <i></i> <span>{{ item.specialName }}</span>
                        </div>
                        <a-button type="link" style="font-size: 12px;" @click="onGoAllQuestion(WrongListType.AllQuestion, item)">查看所有试题></a-button>
                    </div>
                    <div class="desc" v-for="ele in item.itemList" @click="onGoWrongList(WrongListType.Knowledge, ele, item.specialId)">
                        <div class="p1">
                            <span>{{ ele.knowledgeName }}</span
                            ><i class="arrow"></i>
                        </div>
                        <div class="p2">共{{ ele.totalCount }}题 已做{{ ele.doneCount }}题 答错{{ ele.wrongCount }}题</div>
                        <div class="p3">
                            <span
                                :class="{
                                    color5: +calcWrongRate(ele.wrongCount, ele.doneCount) === 0,
                                    color4: +calcWrongRate(ele.wrongCount, ele.doneCount) > 0 && +calcWrongRate(ele.wrongCount, ele.doneCount) <= 30,
                                    color3: +calcWrongRate(ele.wrongCount, ele.doneCount) > 30 && +calcWrongRate(ele.wrongCount, ele.doneCount) <= 60,
                                    color2: +calcWrongRate(ele.wrongCount, ele.doneCount) > 60 && +calcWrongRate(ele.wrongCount, ele.doneCount) <= 80,
                                    color1: +calcWrongRate(ele.wrongCount, ele.doneCount) > 80 && +calcWrongRate(ele.wrongCount, ele.doneCount) <= 100,
                                }"
                                >答错率：{{ calcWrongRate(ele.wrongCount, ele.doneCount) }}%</span
                            >
                        </div>
                    </div>
                </div>
            </swiper-slide>
        </swiper>
    </div>
</template>

<style lang="less" scoped>
.cpage {
    height: 100%;
    width: 100%;
    padding: 15px 0 50px 0;

    :deep(.swiper) {
        height: 100%;
        .swiper-slide {
            width: 350px;
        }
    }
    .cate {
        background: #ffffff;
        border: 0.8px solid #f0f0f0;
        border-radius: 16px;
        padding: 20px 0;
        overflow-y: auto;
        height: 100%;
        .cline {
            display: flex;
            justify-content: space-between;
        }
        .ctitle {
            display: flex;
            align-items: center;
            padding-bottom: 5px;
            i {
                width: 4px;
                height: 16px;
                background: #04a5ff;
                border-radius: 0px 2px 2px 0px;
            }
            span {
                font-size: 16px;
                font-weight: bold;
                text-align: left;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
                padding-left: 16px;
            }
        }

        .desc {
            cursor: pointer;
            padding: 15px 0;
            margin: 0 20px;
            border-bottom: 1px solid #f2f2f2;
            .p1 {
                display: flex;
                align-items: center;
                justify-content: space-between;
                span {
                    font-size: 17px;
                    font-weight: bold;
                    text-align: left;
                    color: rgba(0, 0, 0, 0.85);
                    line-height: 24px;
                }
                i {
                    width: 24px;
                    height: 24px;
                    background: url(./images/arrow.png) no-repeat;
                    background-size: 100% 100%;
                }
            }
            .p2 {
                font-size: 15px;
                text-align: left;
                color: rgba(0, 0, 0, 0.45);
                line-height: 21px;
                margin-top: 6px;
            }
            .p3 {
                display: flex;
                span {
                    border-radius: 4px;
                    font-size: 13px;
                    text-align: left;

                    line-height: 24px;
                    padding: 0 6px;
                    margin-top: 10px;
                    &.color1 {
                        color: #ff4a40;
                        background: rgba(255, 74, 64, 0.08);
                    }
                    &.color2 {
                        color: #ff822d;
                        background: rgba(255, 130, 45, 0.08);
                    }
                    &.color3 {
                        color: #ffa017;
                        background: rgba(255, 160, 23, 0.08);
                    }
                    &.color4 {
                        color: #00d1c6;
                        background: rgba(0, 209, 198, 0.08);
                    }
                    &.color5 {
                        color: #a0a0a0;
                        background: rgba(160, 160, 160, 0.08);
                    }
                }
            }
        }
    }
}
</style>
